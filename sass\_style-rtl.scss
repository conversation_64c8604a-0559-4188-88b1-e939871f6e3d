@if $RTL-template == true {
	/* ----------------------------------------------------------------

	style-rtl.scss

-----------------------------------------------------------------*/

	html,
	body {
		direction: rtl;
		text-align: right;
	}

	.bgicon {
		left: -50px;
		right: auto;
	}

	.vertical-middle-overlay {
		right: 0;
		left: auto;
	}

	/* ----------------------------------------------------------------
		Top Bar
	-----------------------------------------------------------------*/

	/* ----------------------------------------------------------------
		topbar.scss
	-----------------------------------------------------------------*/
	.top-links {
		&-item {
			border-right: $topbar-border-size solid $topbar-border-color;
			border-left: 0;

			&:first-child,
			.top-links-sub-menu & {
				border-right: 0 !important;
			}

			> a {
				> i {
					&.icon-angle-down {
						margin: 0 5px 0 0 !important;
					}
					&:first-child {
						margin-left: 3px;
						margin-right: 0;
					}
				}
			}
		}

		&-sub-menu,
		&-section {
			right: -1px;
			left: auto;
		}

		&-sub-menu {
			.top-links-sub-menu {
				right: 100%;
				left: auto;
			}
			.top-links-item {
				> a {
					> img {
						margin-left: 4px;
						margin-right: 0;
					}

					i.icon-angle-down {
						left: 10px;
						right: auto;
						transform: translateY(-50%) rotate(90deg);
					}
				}
			}
		}
		&-section {
			right: 0;
			left: auto;
			&.menu-pos-invert {
				left: 0;
				right: auto;
			}
		}
	}

	/* Top Social
	-----------------------------------------------------------------*/

	#top-social {
		li {
			border-right: $topbar-border-size solid $topbar-border-color;
			border-left: 0;
			&:first-child {
				border-right: 0 !important;
			}
			a:hover .ts-text {
				padding-left: 12px;
				padding-right: 0;
			}
		}
	}

	/* ----------------------------------------------------------------
		Header
	-----------------------------------------------------------------*/

	#header {
		&-wrap {
			@include media-breakpoint-up(lg) {
				will-change: right, left, top, transform;
			}
		}
	}

	@include media-breakpoint-up(lg) {
		/* ----------------------------------------------------------------
			Sticky Header
		-----------------------------------------------------------------*/

		.sticky-header {
			#header-wrap {
				right: 0;
				left: auto;
			}
		}

		/* Primary Menu - Style 5
		-----------------------------------------------------------------*/

		.sticky-header-shrink .style-5 .menu-container > .menu-item {
			margin-right: 2px;
			margin-left: 0;
			&:first-child {
				margin-right: 0;
			}
			> .menu-link {
				i:not(.icon-angle-down) {
					margin: 0 0 0 6px;
				}
			}
		}
	}

	@include media-breakpoint-down(md) {
		.sticky-header {
			#header-wrap {
				right: 0;
				left: auto;
			}
		}
	}

	/* ----------------------------------------------------------------
		Logo
	-----------------------------------------------------------------*/

	#logo {
		margin-right: 0;
		margin-left: auto;
		@include media-breakpoint-up(lg) {
			.full-header & {
				padding-left: 30px;
				padding-right: 0;
				border-left: 1px solid set-border-color($header-bg);
				border-right: 0;
			}

			#header.transparent-header.full-header & {
				border-left-color: rgba($black, 0.1);
			}
		}
	}

	/* ----------------------------------------------------------------
		Header Right Area
	-----------------------------------------------------------------*/

	.header-extras {
		li {
			margin-right: 1.25rem;
			margin-left: 0;
			&:first-child {
				margin-right: 0;
			}

			.he-text {
				padding-right: 10px;
				padding-left: 0;
			}
		}
	}

	/* ----------------------------------------------------------------
		Primary Menu
	-----------------------------------------------------------------*/

	.menu-item {
		.sub-menu-trigger {
			left: 0;
			right: auto;
		}

		@include media-breakpoint-up(lg) {
			&.menu-item-important .menu-link > div::after {
				right: 5px;
				left: auto;
			}
		}
	}

	.menu-link {
		i {
			margin-left: 6px;
			margin-right: 0;
		}
	}

	.sub-menu-container,
	.mega-menu-content {
		padding-right: 15px;
		padding-left: -0px;
	}

	/* ----------------------------------------------------------------
		Mega Menu
	-----------------------------------------------------------------*/

	.mega-menu-content {
		padding-right: 0;
		padding-left: 0;
	}

	@include media-breakpoint-down(md) {
		.mega-menu-content {
			.mega-menu-column {
				padding-left: 0;
				padding-right: 0;
			}
		}
	}

	/* Off Canvas Menu
	-----------------------------------------------------------------*/

	@include media-breakpoint-down(md) {
		body:not(.sticky-responsive-menu) .mobile-menu-off-canvas {
			.menu-container {
				right: 0 !important;
				left: auto !important;
				border-left: 1px solid set-border-color($header-bg) !important;
				border-right: 0 !important;
			}

			&.from-right .menu-container {
				right: auto !important;
				right: auto !important;
				border-left: 0 !important;
				border-right: 1px solid set-border-color($header-bg) !important;
			}
		}
	}

	@include media-breakpoint-up(lg) {
		.full-header .primary-menu .menu-container {
			padding-left: 0.5rem;
			padding-right: 0;
			margin-left: 0.5rem;
			margin-right: 0;
			border-left: 1px solid set-border-color($header-bg);
			border-right: 0;
		}

		.transparent-header.full-header .primary-menu .menu-container {
			border-left-color: rgba($black, 0.1);
		}

		.menu-container > .menu-item:not(:first-child) {
			margin-right: 2px;
			margin-left: 0;
		}

		.sub-menu-container .sub-menu-container {
			right: 100%;
			left: auto;
		}

		.sub-menu-container.menu-pos-invert,
		.mega-menu-content.menu-pos-invert {
			right: auto;
			left: 0;
		}

		.sub-menu-container .sub-menu-container.menu-pos-invert {
			left: 100%;
			right: auto;
		}

		.sub-menu-container .menu-item:hover > .menu-link {
			padding-right: 18px;
			padding-left: 0;
		}

		.menu-link div > i.icon-angle-down {
			margin: 0 5px 0 0;
		}

		.sub-menu-container .menu-link div > i.icon-angle-down,
		.side-header .primary-menu:not(.on-click) .menu-link i.icon-angle-down {
			right: auto;
			left: 0;
			transform: translateY(-50%) rotate(90deg);
		}

		/* ----------------------------------------------------------------
			Mega Menu
		-----------------------------------------------------------------*/

		.mega-menu-small {
			.mega-menu-content {
				right: 0;
				left: auto;
			}
		}

		.sub-menu-container .mega-menu-small .mega-menu-content {
			right: 100%;
			left: auto;
		}

		.sub-menu-container
			.mega-menu-small
			.mega-menu-content.menu-pos-invert {
			right: auto;
			left: 100%;
		}

		.mega-menu-content .sub-menu-container:not(.mega-menu-dropdown) {
			right: 0;
			left: auto;
		}

		.sub-menu-container.mega-menu-column:not(:first-child) {
			border-right: 1px solid $line-color;
			border-left: 0;
		}

		.mega-menu-style-2 .sub-menu-container .menu-link {
			padding-right: 5px;
			padding-left: 0;
		}

		.mega-menu-style-2 .sub-menu-container .menu-item:hover > .menu-link {
			padding-right: 12px;
			padding-left: 0;
		}

		/* Primary Menu - Style 2
		-----------------------------------------------------------------*/

		.style-2 #logo {
			margin-left: 1.5rem;
			margin-right: 0;
		}

		.style-2 .primary-menu {
			margin-left: auto;
			margin-right: 0;
		}

		/* Primary Menu - Style 5
		-----------------------------------------------------------------*/

		.style-5 .menu-container {
			padding-left: 10px;
			padding-right: 0;
			margin-left: 5px;
			margin-right: 0;
			border-left: 1px solid set-border-color($header-bg);
			border-right: 0;
		}

		.style-5 .menu-container > .menu-item:not(:first-child) {
			margin-right: 15px;
			margin-left: 0;
		}

		/* Primary Menu - Style 6
		-----------------------------------------------------------------*/
		.style-6 .menu-container > .menu-item > .menu-link::after,
		.style-6 .menu-container > .menu-item.current > .menu-link::after {
			right: 0;
			left: auto;
		}

		/* Primary Menu - Sub Title
		-----------------------------------------------------------------*/

		.sub-title .menu-container > .menu-item {
			margin-right: 0;
		}

		.sub-title
			.menu-container
			> .menu-item:not(:first-child)
			> .menu-link::before {
			right: 0;
			left: auto;
		}

		.sub-title .menu-container > .menu-item:hover > .menu-link::after,
		.sub-title .menu-container > .menu-item.current > .menu-link::after {
			right: 0;
			left: auto;
		}

		/* ----------------------------------------------------------------
			Side Header
		-----------------------------------------------------------------*/

		.side-header:not(.open-header) #wrapper {
			margin: 0 ($side-header-width) 0 0 !important;
		}

		.side-header #header {
			right: 0;
			left: auto;
			border-left: 1px solid $line-color;
			border-right: 0;
		}

		.side-header #header-wrap {
			padding-left: 40px;
			padding-right: 0;
		}

		.side-header .on-click .menu-item .sub-menu-trigger {
			left: -5px;
			right: auto;
		}

		.side-header
			.primary-menu:not(.on-click)
			.text-center
			i.icon-angle-down {
			margin-right: 5px;
			margin-left: 0;
		}

		.side-header .sub-menu-container,
		.side-header .mega-menu-content {
			right: 0;
			left: auto;
			padding: 0 12px 0 0 !important;
		}

		.side-header .sub-menu-container .sub-menu-container {
			right: 0;
			left: auto;
		}

		.side-header .sub-menu-container.menu-pos-invert {
			right: 0;
			left: auto;
		}

		.side-header .sub-menu-container .sub-menu-container.menu-pos-invert {
			left: auto;
			right: 0;
		}

		/* Side Header - Push Header
		-----------------------------------------------------------------*/

		.side-header.open-header #header {
			right: -($side-header-width);
			left: auto;
		}

		.side-header.open-header.side-header-open #header {
			right: 0;
			left: auto;
		}

		.side-header.open-header #wrapper {
			right: 0;
			left: auto;
		}

		.side-header.open-header.push-wrapper.side-header-open #wrapper {
			right: ($side-header-width);
			left: auto;
		}

		.side-header.open-header #header-trigger {
			right: 20px;
			left: auto;
		}

		.side-header.open-header.side-header-open #header-trigger {
			right: 280px;
			left: auto;
		}

		.side-header.open-header #header,
		.side-header.open-header #header-trigger,
		.side-header.open-header.push-wrapper #wrapper {
			@include transition(right 0.4s ease);
		}

		/* Side Header - Right Aligned
		-----------------------------------------------------------------*/

		.side-header.side-header-right #header {
			right: auto;
			left: 0;
			border-right: 1px solid $line-color;
			border-left: 0;
		}

		.side-header.side-header-right:not(.open-header) #wrapper {
			margin: 0 0 0 ($side-header-width) !important;
		}

		.side-header.side-header-right.open-header #header {
			right: auto;
			left: -($side-header-width);
		}

		.side-header.side-header-right.open-header.side-header-open #header {
			right: auto;
			left: 0;
		}

		.side-header.side-header-right.open-header.push-wrapper.side-header-open
			#wrapper {
			right: -($side-header-width);
			left: auto;
		}

		.side-header.side-header-right.open-header #header-trigger {
			right: auto;
			left: 20px;
		}

		.side-header.side-header-right.open-header.side-header-open
			#header-trigger {
			right: auto;
			left: 280px;
		}

		.side-header.side-header-right.open-header #header,
		.side-header.side-header-right.open-header #header-trigger {
			@include transition(right 0.4s ease, left 0.4s ease);
		}

		/* Primary Menu - Overlay Menu
		-----------------------------------------------------------------*/

		.overlay-menu .primary-menu > #overlay-menu-close {
			right: auto;
			left: 25px;
		}

		.overlay-menu .primary-menu {
			right: 0;
			left: auto;
		}

		.overlay-menu .sub-menu-container,
		.overlay-menu .mega-menu-content {
			right: 0 !important;
			left: auto !important;
		}

		.overlay-menu .sub-menu-container .menu-link i.icon-angle-down {
			right: 0;
			left: auto;
			margin-right: 5px;
			margin-left: 0;
		}
	}

	@include media-breakpoint-down(md) {
		.side-push-panel #side-panel-trigger-close a {
			right: auto;
			left: 0;
			border-radius: 0 2px 0 0;
		}
	}

	/* ----------------------------------------------------------------
		Top Search
	-----------------------------------------------------------------*/

	#top-search {
		a {
			i {
				right: 3px;
				left: auto;
			}
		}
	}

	.top-search-form {
		right: 0;
		left: auto;
		input {
			padding: 10px 0 10px 80px;
		}
	}

	@include media-breakpoint-up(lg) {
		.full-header .top-search-form input,
		.container-fluid .top-search-form input {
			padding-right: 30px;
			padding-left: 0;
		}
	}

	.search-overlay .top-search-form {
		right: 0;
		left: auto;
	}

	/* ----------------------------------------------------------------
		Top Cart
	-----------------------------------------------------------------*/
	.top-cart {
		&-number {
			right: auto;
			left: -8px;
		}
		&-content {
			right: auto;
			left: 0;
			border-right: 1px solid set-border-color($header-bg);
			border-left: 0;
			@include transition(left 0.3s ease, right 0.3s ease);
			@include media-breakpoint-down(md) {
				left: -260px !important;
				right: auto !important;
			}
			@include media-breakpoint-up(lg) {
				left: 0;
				right: auto;
			}
		}
		&-item,
		&-action,
		&-item-desc {
			-ms-flex-align: end;
			align-items: flex-end;
		}
		&-item {
			-ms-flex-pack: end;
			justify-content: flex-end;
			&-desc,
			&-desc-title {
				padding-right: 1rem;
				padding-left: 0;
			}
			&-desc {
				-ms-flex-align: end;
				align-items: flex-end;
				&-title {
					padding-right: 0;
				}
			}
		}
		&-open .top-cart-content {
			@include media-breakpoint-down(md) {
				left: 0 !important;
				right: auto !important;
			}
		}
	}

	/* ----------------------------------------------------------------
		Page Menu
	-----------------------------------------------------------------*/
	#page-menu-wrap {
		right: 0;
		left: auto;
	}

	#page-menu-trigger {
		right: auto;
		left: 15px;
	}

	.page-menu-title {
		margin-left: auto;
		margin-right: 0;
	}
	.page-menu-nav {
		right: auto;
		left: 0;
		@include media-breakpoint-up(lg) {
			right: 0;
			left: auto;
		}
	}
	.page-menu-item > a .icon-angle-down {
		right: auto;
		left: 14px;
		@include media-breakpoint-up(lg) {
			margin-right: 8px;
			margin-left: 0;
		}
	}
	@include media-breakpoint-up(lg) {
		.page-menu-sub-menu {
			right: 0;
			left: auto;
		}
	}

	/* Page Menu - Dots Style
	-----------------------------------------------------------------*/
	@include media-breakpoint-up(lg) {
		#page-menu.dots-menu {
			right: auto;
			left: 20px;
		}

		.dots-menu .page-menu-item div {
			left: 25px;
			right: auto;
		}
		.dots-menu .page-menu-item div::after {
			right: auto;
			left: -5px;
			border-right: 6px solid var(--#{$cnvs-prefix}themecolor);
			border-left: 0;
		}
		.dots-menu .page-menu-item > a:hover div {
			left: 30px;
			right: auto;
		}
	}

	/* ----------------------------------------------------------------
		One Page
	-----------------------------------------------------------------*/
	.one-page-arrow {
		right: 50%;
		left: auto;
		margin-right: -16px;
		margin-left: 0;
	}

	/* ----------------------------------------------------------------
		Side Push Panel
	-----------------------------------------------------------------*/

	#side-panel {
		left: -$side-panel-size;
		right: auto;
		.side-panel-left & {
			right: -$side-panel-size;
			left: auto;
		}
	}

	.side-push-panel {
		&.stretched #wrapper,
		&.stretched .sticky-header .container {
			left: 0;
			right: auto;
		}
	}

	.side-panel-left.side-push-panel.stretched #wrapper,
	.side-panel-left.side-push-panel.stretched .sticky-header .container {
		right: 0;
		left: auto;
	}

	#side-panel {
		.side-panel-wrap {
			right: 0;
			left: auto;
		}
		.side-panel-open & {
			left: 0;
			right: auto;
		}
		.side-panel-left.side-panel-open & {
			right: 0;
			left: auto;
		}
	}

	.side-push-panel.side-panel-open.stretched #wrapper,
	.side-push-panel.side-panel-open.stretched .sticky-header .container {
		left: $side-panel-size;
		right: auto;
	}

	.side-push-panel.side-panel-open.stretched.device-xl .slider-inner,
	.side-push-panel.side-panel-open.stretched.device-lg .slider-inner {
		right: -$side-panel-size;
		left: auto;
	}

	.side-panel-left.side-push-panel.side-panel-open.stretched #wrapper,
	.side-panel-left.side-push-panel.side-panel-open.stretched
		.sticky-header
		.container,
	.side-panel-left.side-push-panel.side-panel-open.stretched .slider-inner {
		right: $side-panel-size;
		left: auto;
	}

	#side-panel-trigger {
		margin-left: 5px;
		margin-right: 0;
	}

	#side-panel,
	.side-push-panel.stretched #wrapper,
	.side-push-panel.stretched #header .container {
		@include transition(right 0.4s ease, left 0.4s ease);
	}

	.side-panel-left #side-panel,
	.side-panel-left.side-push-panel.stretched #wrapper,
	.side-panel-left.side-push-panel.stretched #header .container,
	.side-push-panel.stretched .slider-inner {
		@include transition(right 0.4s ease, left 0.4s ease);
	}

	.body-overlay {
		right: 0;
		left: auto;
	}

	/* ----------------------------------------------------------------
		Slider
	-----------------------------------------------------------------*/
	body:not(.side-header) .slider-parallax .slider-inner {
		right: 0;
		left: auto;
	}

	body:not(.stretched) .slider-parallax .slider-inner {
		right: auto;
		left: 0;
	}

	/* Swiper Slider
	-----------------------------------------------------------------*/

	.slider-element .video-wrap,
	.section .video-wrap,
	.swiper-slide .yt-bg-player,
	.section .yt-bg-player,
	.swiper-slide-bg {
		right: 0;
		left: auto;
	}

	/* Video Overlay
	-----------------------------------------------------------------*/

	.video-overlay,
	.video-placeholder {
		right: 0;
		left: auto;
	}

	/* Slider Caption
	-----------------------------------------------------------------*/

	.slider-caption.slider-caption-right {
		margin-left: 0;
		margin-right: auto;
	}

	.slider-caption-bg {
		right: $slider-caption-offset;
		left: auto;
	}

	.slider-caption-top-right {
		right: auto;
		left: $slider-caption-offset;
	}

	.slider-caption-bottom-right {
		right: auto;
		left: $slider-caption-offset;
	}

	/* Slide Number
	-----------------------------------------------------------------*/

	.slide-number {
		left: 20px;
		right: auto;
	}

	.slide-number-current,
	.slide-number-total {
		right: 0;
		left: auto;
	}

	.slide-number-total {
		right: auto;
		left: 0;
	}

	/* Flex Slider - Navigation
	-----------------------------------------------------------------*/

	.nav-offset .flex-prev {
		right: 20px;
		left: auto;
	}

	.nav-offset .flex-next {
		left: 20px;
		right: auto;
	}

	.nav-pos-top .flex-direction-nav,
	.nav-pos-top-left .flex-direction-nav,
	.nav-pos-top-right .flex-direction-nav {
		right: 0;
		left: auto;
	}

	.nav-pos-top-left .flex-direction-nav,
	.nav-pos-bottom-left .flex-direction-nav {
		right: 16px;
		left: auto;
	}

	.nav-pos-top-right .flex-direction-nav,
	.nav-pos-bottom-right .flex-direction-nav {
		right: auto;
		left: 16px;
	}

	/* Flex Slider - Pagination & Thumbs
	-----------------------------------------------------------------*/

	.flex-control-nav {
		left: $fslider-dots-position-right;
		right: auto;
	}

	/* Flex Slider: Pagination Positions
	-----------------------------------------------------------------*/

	.control-offset-lg .flex-control-nav {
		left: $fslider-dots-position-right + 10;
		right: auto;
	}

	.control-pos-top-left .flex-control-nav,
	.control-pos-bottom-left .flex-control-nav {
		right: $fslider-dots-position-right;
		left: auto;
	}

	.control-offset-lg.control-pos-top-left .flex-control-nav,
	.control-offset-lg.control-pos-bottom-left .flex-control-nav {
		right: $fslider-dots-position-right + 10;
		left: auto;
	}

	.control-pos-bottom .flex-control-nav,
	.control-pos-bottom .flex-control-nav,
	.control-pos-top .flex-control-nav,
	.control-pos-top .flex-control-nav {
		left: 0;
		right: auto;
	}

	/* Flex Slider: Thumbs
	-----------------------------------------------------------------*/

	.flex-control-nav.flex-control-thumbs {
		justify-content: right;
		margin: $fslider-thumbs-gutters
			0 -$fslider-thumbs-gutters -$fslider-thumbs-gutters;
	}

	.flex-control-nav.flex-control-thumbs li {
		margin: 0 0 $fslider-thumbs-gutters $fslider-thumbs-gutters;
	}

	/* FlexSlider: Thumbs - Flexible
	-----------------------------------------------------------------*/

	.fslider.flex-thumb-grid .flex-control-nav.flex-control-thumbs {
		margin: $fslider-thumbs-gutters
			0 -$fslider-thumbs-gutters -$fslider-thumbs-gutters;
	}

	.fslider.flex-thumb-grid .flex-control-nav.flex-control-thumbs li {
		padding: 0 0 $fslider-thumbs-gutters $fslider-thumbs-gutters;
	}

	/* ----------------------------------------------------------------
		Page Title
	-----------------------------------------------------------------*/
	#page-title {
		text-align: right;
	}

	#page-title .breadcrumb {
		right: auto !important;
		left: 15px !important;
	}

	/* Page Title - Right Aligned
	-----------------------------------------------------------------*/

	#page-title .page-title-right {
		text-align: left;
	}

	#page-title .page-title-right .breadcrumb {
		right: 15px !important;
		left: auto !important;
	}

	#page-title .page-title-video .video-wrap {
		right: 0;
		left: auto;
	}

	/* ----------------------------------------------------------------
		Portfolio
	-----------------------------------------------------------------*/

	/* Grid - Filter
	-----------------------------------------------------------------*/

	.grid-filter {
		li {
			a {
				@include media-breakpoint-up(md) {
					border-right: 1px solid rgba(0, 0, 0, 0.07);
					border-left: 0;
				}
			}
			@include media-breakpoint-up(md) {
				&:first-child a {
					border-right: none;
					border-radius: $grid_filter-border-radius
						$grid_filter-border-radius 0 0;
				}
				&:last-child a {
					border-radius: 0 0 $grid_filter-border-radius
						$grid_filter-border-radius;
				}
			}
		}

		/* Grid - Filter: Style 2
		-----------------------------------------------------------------*/
		@include media-breakpoint-up(md) {
			&.style-2,
			&.style-3 {
				li:not(:first-child) {
					margin-right: $grid_filter-styles-m;
					margin-left: 0;
				}
			}
		}

		/* Portfolio - Filter: Style 4
		-----------------------------------------------------------------*/
		&.style-4 li {
			@include media-breakpoint-up(md) {
				&:not(:first-child) {
					margin-right: 30px;
					margin-left: 0;
				}
			}

			a {
				&::after {
					right: 50%;
					left: auto;
				}
			}
			&.activeFilter a {
				&::after {
					right: 0%;
					left: auto;
				}
			}
		}
	}

	/* Portfolio Single - Meta
	-----------------------------------------------------------------*/

	.portfolio-meta li span i {
		margin-left: $portfolio-meta-icon / 2;
		margin-right: 0;
	}
	/* Portfolio Single Navigation
	-----------------------------------------------------------------*/

	#portfolio-navigation {
		right: auto;
		left: 10px;
		.page-title-right & {
			right: 10px;
			left: auto;
		}

		.page-title-center & {
			right: 0;
			left: auto;
		}
		a {
			float: right;
			margin-right: ($portfolio-navigation-size / 2);
			margin-left: 0;
			&:first-child {
				margin-right: 0;
			}
			i {
				&.icon-angle-right {
					right: -1px;
					left: auto;
				}
			}
		}
	}

	/* ----------------------------------------------------------------
		Blog
	-----------------------------------------------------------------*/

	.entry-meta {
		ul {
			margin: 0 -20px -10px;
		}
	}

	.entry-meta.no-separator ul,
	.entry-meta ul.flex-column {
		align-items: flex-end;
		margin-right: 0;
	}

	.entry-meta li {
		margin: 0 0 10px 15px;
	}

	.entry-meta ul.flex-column li {
		margin-right: 0;
	}

	.entry-meta:not(.no-separator) li::before {
		margin-left: $post-meta-margin;
		margin-right: 0;
	}

	.entry-meta i {
		padding-right: 1px;
		padding-left: 0;
		margin-left: 5px;
		margin-right: 0;
	}

	/* Blog - Timeline
	-----------------------------------------------------------------*/

	@include media-breakpoint-up(lg) {
		.postcontent .post-timeline::before {
			right: 50%;
			left: auto;
			border-right: 1px dashed $post-timeline-border-color;
			border-left: 0;
		}

		.post-timeline .entry-timeline {
			right: auto;
			left: -6px;
		}

		.post-timeline .entry-timeline .timeline-divider {
			right: auto;
			left: 15px;
		}

		.post-timeline .entry:not(.entry-date-section) {
			padding-left: 85px;
			padding-right: 0;
		}

		.post-timeline .entry.alt:not(.entry-date-section) {
			padding-left: inherit;
			padding-right: 85px;
		}

		.post-timeline .alt .entry-timeline {
			left: auto;
			right: -6px;
		}

		.post-timeline .alt .entry-timeline .timeline-divider {
			right: 15px;
			left: auto;
		}
	}

	@include media-breakpoint-up(lg) {
		/* Blog - Timeline - Sidebar
		-----------------------------------------------------------------*/

		.postcontent .post-timeline::before {
			right: -$post-timeline-date-size - $post-timeline-date-border -
				$post-timeline-border-size;
			left: auto;
			margin-right: 0;
			margin-left: 0;
		}

		.postcontent .post-timeline {
			padding-right: 0;
			margin-right: 100px !important;
			margin-left: 0 !important;
		}

		.postcontent .post-timeline .entry-timeline {
			right: -100px;
			left: auto;
		}

		.postcontent .post-timeline .entry-timeline div.timeline-divider {
			right: 64px;
			left: auto;
		}
	}

	/* ----------------------------------------------------------------
		Blog - Author
	-----------------------------------------------------------------*/

	.author {
		&-image {
			float: right;
			margin-left: 15px;
			margin-right: 0;
		}
	}

	/* ----------------------------------------------------------------
		Comments List
	-----------------------------------------------------------------*/

	.commentlist li {
		margin: 30px 30px 0 0;
	}

	.comment-wrap {
		padding: 20px 35px 20px 20px;
	}

	.commentlist ul .comment-wrap {
		margin-right: 25px;
		margin-left: 0;
		padding-right: 20px;
		padding-left: 0;
	}

	#reviews .comment-wrap {
		padding: 10px 35px 0 0;
	}

	.commentlist li li .children {
		margin-right: 30px;
		margin-left: 0;
	}

	.commentlist li .comment-content {
		padding: 0 15px 0 0;
	}

	.commentlist li .comment-meta {
		float: right;
		margin-left: 0;
	}

	.comment-avatar {
		right: -35px;
		left: auto;
	}

	.commentlist li .children .comment-avatar {
		right: -25px;
		left: auto;
	}

	.comment-reply-link,
	.review-comment-ratings {
		right: auto;
		left: 0px;
	}

	/* ----------------------------------------------------------------
		Comment Form
	-----------------------------------------------------------------*/

	.commentlist li li #respond {
		margin-right: 30px;
		margin-left: 0;
	}

	/* Post Elements
	-----------------------------------------------------------------*/

	img.alignleft,
	div.alignleft {
		float: right;
		margin: 5px 0px 13px 20px;
	}

	img.alignright,
	div.alignright {
		float: left;
		margin: 5px 20px 13px 0px;
	}

	/* Infinity Scroll - Message Style
	-----------------------------------------------------------------*/

	#infscr-loading,
	#portfolio-ajax-loader {
		right: 50%;
		left: auto;
		margin: -24px -24px 0 0;
	}

	/* ----------------------------------------------------------------
		Shop
	-----------------------------------------------------------------*/

	.product-image > a:nth-of-type(2) {
		right: 0;
		left: auto;
	}

	.product-image > .badge,
	.sale-flash {
		right: 10px;
		left: auto;
	}

	/* Shop - Single
	-----------------------------------------------------------------*/

	.quantity {
		margin-left: 30px;
		margin-right: 0;
	}

	/* ----------------------------------------------------------------
		Events
	-----------------------------------------------------------------*/

	.event .entry-image .entry-date {
		right: 10px;
		left: auto;
	}

	.single-event .entry-overlay,
	.parallax .entry-overlay-meta {
		right: 0;
		left: auto;
	}

	.single-event .countdown-section {
		border-right-color: rgba($white, 0.3);
		border-left-color: transparent;
	}

	.parallax .entry-overlay,
	.parallax .entry-overlay-meta {
		text-align: left;
		right: auto;
		left: 30px;
	}

	.parallax .entry-overlay-meta {
		text-align: right;
		left: 38px;
		right: auto;
	}

	.parallax.overlay-left .entry-overlay,
	.parallax.overlay-left .entry-overlay-meta {
		right: 30px;
		left: auto;
	}

	.parallax.overlay-left .entry-overlay-meta {
		right: 38px;
		left: auto;
	}

	.parallax.overlay-center .entry-overlay,
	.parallax.overlay-center .entry-overlay-meta {
		right: 50%;
		left: auto;
		margin-right: -round(($event-parallax-overlay-width + 16) / 2);
		margin-right: 0;
	}

	.parallax.overlay-center .entry-overlay-meta {
		margin-right: -$event-parallax-overlay-width / 2;
		margin-left: 0;
	}

	.parallax .countdown-section {
		border-right: 0;
	}

	/* ----------------------------------------------------------------

		Countdown

	-----------------------------------------------------------------*/

	.countdown {
		&-section {
			border-right: $countdown-border;
			border-left: 0;
		}

		&-section:first-child {
			border-right: 0;
		}

		/* Countdown - Inline
		-----------------------------------------------------------------*/
		&#{&}-inline {
			.countdown-section {
				margin-right: $countdown-inline-space;
				margin-left: 0;
				&:first-child {
					margin-right: 0;
				}
			}
			.countdown-amount {
				margin: 0 0 0 3px;
			}
		}
	}

	/* ----------------------------------------------------------------
		Buttons
	-----------------------------------------------------------------*/

	.button {
		@include gradient-bg($button-theme-color);
		i {
			margin-left: $button-icon-margin;
			margin-right: 0;
		}
		&.text-end {
			i {
				margin: 0 $button-icon-margin 0 0;
			}
		}
		&-mini {
			i {
				margin-left: $button-icon-margin - 2;
				margin-right: 0;
			}
			&.text-end i {
				margin: 0 ($button-icon-margin - 2) 0 0;
			}
		}

		&-small {
			i {
				margin-left: $button-icon-margin - 1;
				margin-right: 0;
			}
			&.text-end i {
				margin: 0 ($button-icon-margin - 1) 0 0;
			}
		}

		&-xlarge {
			i {
				margin-left: $button-icon-margin + 3;
				margin-right: 0;
			}
			&.text-end i {
				margin: 0 ($button-icon-margin + 3) 0 0;
			}
		}

		&-desc {
			text-align: right;
			i {
				margin-left: 12px;
				margin-right: 0;
			}
			&.text-end i {
				margin: 0 12px 0 0;
			}
		}

		/* Buttons - Icon Reveal
		-----------------------------------------------------------------*/
		$button-reveal-mini-icon: ($button-reveal-icon - 10);
		$button-reveal-small-icon: ($button-reveal-icon - 6);
		$button-reveal-lg-icon: ($button-reveal-icon + 6);
		$button-reveal-xl-icon: ($button-reveal-icon + 12);

		&.button-reveal {
			i {
				right: -$button-reveal-icon;
				left: auto;
			}
			&.text-end i {
				right: auto;
				left: -$button-reveal-icon;
			}
			span {
				right: 0;
				left: auto;
			}

			&.button-mini {
				i {
					right: -($button-reveal-mini-icon);
					left: auto;
				}
				&.text-end i {
					right: auto;
					left: -($button-reveal-mini-icon);
				}
				&:hover span {
					right: round($button-reveal-mini-icon / 2);
					left: auto;
				}
				&.text-end:hover span {
					right: -round($button-reveal-mini-icon / 2);
					left: auto;
				}
			}

			&.button-small {
				i {
					right: -$button-reveal-small-icon;
					left: auto;
				}
				&.text-end i {
					right: auto;
					left: -$button-reveal-small-icon;
				}
				&:hover span {
					right: round($button-reveal-small-icon / 2);
					left: auto;
				}
				&.text-end:hover span {
					right: -round($button-reveal-small-icon / 2);
					left: auto;
				}
			}

			&.button-large {
				i {
					right: -($button-reveal-lg-icon);
					left: auto;
				}
				&.text-end i {
					right: auto;
					left: -($button-reveal-lg-icon);
				}
				&:hover span {
					right: round($button-reveal-lg-icon / 2);
					left: auto;
				}
				&.text-end:hover span {
					right: -round($button-reveal-lg-icon / 2);
					left: auto;
				}
			}

			&.button-xlarge {
				i {
					right: -($button-reveal-xl-icon);
					left: auto;
				}
				&.text-end i {
					right: auto;
					left: -($button-reveal-xl-icon);
				}
				&:hover span {
					right: round($button-reveal-xl-icon / 2);
					left: auto;
				}
				&.text-end:hover span {
					right: -round($button-reveal-xl-icon / 2);
					left: auto;
				}
			}

			&:hover i {
				right: 0;
				left: auto;
			}
			&.text-end:hover i {
				right: auto;
				left: 0;
			}
			&:hover span {
				right: round($button-reveal-icon / 2);
				left: auto;
			}
			&.text-end:hover span {
				right: -round($button-reveal-icon / 2);
				left: auto;
			}
		}
	}

	/* Buttons - Fill Effect
	-----------------------------------------------------------------*/

	.button {
		&.button-border {
			&.button-fill {
				&::before {
					right: 0;
					left: auto;
				}

				&.fill-from-right::before {
					right: auto;
					left: 0;
				}
			}
		}
	}

	/* Promo - Mini
	-----------------------------------------------------------------*/

	.promo-mini {
		text-align: right;
	}

	/* ----------------------------------------------------------------
		Featured Boxes
	-----------------------------------------------------------------*/

	.fbox-effect {
		.fbox-icon i {
			&::after {
				right: -3px;
				left: auto;
			}
		}
	}

	/* Icon Effects - Bordered
	-----------------------------------------------------------------*/

	.fbox-border {
		&.fbox-effect {
			.fbox-icon i {
				&::after {
					right: -2px;
					left: auto;
				}
			}
		}
	}

	/* Media Featured Box
	-----------------------------------------------------------------*/
	.media-box {
		&.fbox-bg {
			.fbox-media {
				img {
					border-radius: 5px 0 0 5px;
				}
			}
			.fbox-content {
				border-radius: 0 5px 5px 0;
			}
		}
	}

	/* ----------------------------------------------------------------
		Flipbox
	-----------------------------------------------------------------*/
	/* ----------------------------------------------------------------
		Flip Cards
	-----------------------------------------------------------------*/

	.flip-card {
		&-front,
		&-back {
			&::after {
				right: 0;
				left: auto;
			}
		}

		&-back {
			right: 0;
			left: auto;
		}

		&-inner {
			right: 0;
			left: auto;
		}
	}

	/* ----------------------------------------------------------------
		Process Steps
	-----------------------------------------------------------------*/

	.process-steps {
		li {
			@include media-breakpoint-up(lg) {
				&::before,
				&::after {
					right: 0;
					left: auto;
					margin-right: -26px;
					margin-left: 0;
				}
				&::after {
					right: auto;
					left: 0;
					margin: 0 0 0 -26px;
				}
			}
		}
	}

	/* ----------------------------------------------------------------
		Alerts
	-----------------------------------------------------------------*/

	.style-msg,
	.style-msg2 {
		border-right: $alert-left-border solid $alert-left-border-color;
		border-left: 0;
	}

	.style-msg2 {
		border-right-color: $alert-left-border-color;
	}

	.style-msg .sb-msg,
	.style-msg2 .msgtitle,
	.style-msg2 .sb-msg {
		border-right: 1px solid rgba(255, 255, 255, 0.5);
		border-left: 0;
	}

	.style-msg2 ol,
	.style-msg2 ul {
		margin: 0 30px 0 0px;
	}

	.alert i,
	.sb-msg i,
	.msgtitle i {
		margin-left: 5px;
		margin-right: 0;
	}

	.style-msg .btn-close {
		right: auto;
		left: 0px;
	}

	/* ----------------------------------------------------------------
		Styled Icons
	-----------------------------------------------------------------*/

	.i-rounded,
	.i-plain,
	.i-circled,
	.i-bordered {
		float: right;
		margin: 4px 0 7px 11px;
		&.i-small {
			margin: 4px 0 7px 11px;
		}
		&.i-medium {
			margin: 6px 0px 9px 13px;
		}
		&.i-large,
		&.i-xlarge {
			margin: 4px 0 7px 11px;
		}
		&.i-xxlarge {
			margin: 6px 0 10px 15px;
		}
	}

	.social-icon {
		float: right;
		margin: 4px 0 7px 11px;
	}

	/* ----------------------------------------------------------------
		Social Icons
	-----------------------------------------------------------------*/

	.social-icon {
		margin: 0 0 $social-margins $social-margins;
	}

	/* Social Icons - Large
	-----------------------------------------------------------------*/

	.social-icon.si-large {
		margin: 0 0 ($social-margins * 2) ($social-margins * 2);
	}
	/* Social Icons - Sticky
	-----------------------------------------------------------------*/

	.si-sticky {
		right: 5px;
		left: auto;
	}

	/* Social Icons - Sticky Right
	-----------------------------------------------------------------*/

	.si-sticky.si-sticky-right {
		right: auto;
		left: 8px;
	}

	/* Social Icons - Share
	-----------------------------------------------------------------*/

	.si-share {
		padding-right: 5px;
		padding-left: 0;
	}

	/* ----------------------------------------------------------------
		Tabs
	-----------------------------------------------------------------*/

	.tab-nav li {
		border-right: 0;
	}

	.tab-nav li:first-child {
		border-right: $tabs-border-width solid $tabs-border-color;
		border-left: 0;
	}

	/* Tabs - Alternate Nav
	-----------------------------------------------------------------*/

	.tabs-alt .tab-nav li:first-child {
		border-right: 0;
	}

	/* Tabs - Navigation Style 2
	-----------------------------------------------------------------*/

	.tab-nav.tab-nav2 li {
		margin-right: 10px;
		margin-left: 0;
	}

	.tab-nav.tab-nav2 li:first-child {
		margin-right: 0;
		border-right: 0;
	}

	/* Tabs - Large Navigation
	-----------------------------------------------------------------*/

	.tab-nav-lg.tab-nav-section li.ui-tabs-active::after {
		right: 50%;
		left: auto;
		margin-right: -16px;
		margin-left: 0;
	}

	.tab-nav-lg.tab-nav-section li.ui-tabs-active::before {
		right: 50%;
		left: auto;
		margin-right: -16px;
		margin-left: 0px;
	}

	/* Tabs - Content Area
	-----------------------------------------------------------------*/
	.tab-nav i {
		margin-left: 3px;
		margin-right: 0;
	}

	/* Tabs - Bordered
	-----------------------------------------------------------------*/

	.tabs-bordered .tab-nav li:first-child {
		margin-right: 0;
	}

	/* Tabs - Side Tabs
	-----------------------------------------------------------------*/

	@include media-breakpoint-up(md) {
		.side-tabs {
			.tab-nav {
				border-left: $tabs-border-width solid $tabs-border-color;
				border-right: 0;
				li {
					border-right: 0;
					border-left: 0;
					text-align: right;
					&:first-child {
						margin-right: 0;
						border-right: $tabs-border-width solid
							$tabs-border-color;
						border-left: 0;
					}
					&.ui-tabs-active a {
						left: -$tabs-border-width;
						right: auto;
					}
				}
				&.tab-nav-lg li i {
					margin-left: 5px;
					margin-right: 0;
				}
			}
			.tab-container {
				padding: 15px $tab-container-padding 0 0;
			}
		}

		.ui-tabs .ui-tabs-hide {
			right: -10000px !important;
			left: auto !important;
		}

		/* Tabs - Side Tabs & Nav Style 2
		-----------------------------------------------------------------*/

		.side-tabs {
			.tab-nav.tab-nav2 {
				border: 0;
				li {
					border: 0;
					margin: 6px 0 0;
					&:first-child {
						margin-top: 0;
					}
					a {
						box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
					}
					&.ui-state-active a {
						right: 0;
					}
				}
			}
		}
	}

	/* Tabs - Side Tabs & Bordered
	-----------------------------------------------------------------*/

	.tabs-bordered.side-tabs .tab-nav {
		margin-right: -$tabs-border-width;
		margin-left: 0;
	}

	.tabs-bordered.side-tabs .tab-nav li.ui-tabs-active a {
		left: -$tabs-border-width;
		right: auto;
		border-left: $tabs-border-width solid $body-bg;
		border-right: 0;
	}

	/* ----------------------------------------------------------------
		Side Navigation
	-----------------------------------------------------------------*/

	.sidenav li a {
		padding: $sidenav-padding-height
			$sidenav-padding-width
			$sidenav-padding-height
			($sidenav-padding-width + $sidenav-icon-margin);
		border-left-width: round($sidenav-border + 1);
		border-right-width: 0;
	}

	.sidenav > li:first-child > a {
		border-radius: $sidenav-radius 0px 0 $sidenav-radius;
	}

	.sidenav > li:last-child > a {
		border-radius: 0 $sidenav-radius $sidenav-radius 0px;
	}

	.sidenav li li a {
		padding: round($sidenav-padding-height / 2)
			round($sidenav-padding-width * 2.5)
			round($sidenav-padding-height / 2)
			round($sidenav-padding-width * 1.43);
	}

	.sidenav li i {
		margin-left: $sidenav-icon-margin;
		margin-right: 0;
	}

	/* Chevrons */
	.sidenav .icon-chevron-right {
		left: auto;
		right: $sidenav-icon-margin;
		margin-left: 0;
	}

	/* ----------------------------------------------------------------
		Clients
	-----------------------------------------------------------------*/

	.clients-grid {
		.grid-item {
			&::before,
			&::after {
				left: auto;
				right: 0;
			}
		}
	}

	/* Testimonials - Grid
	-----------------------------------------------------------------*/
	.testimonials-grid {
		.grid-item {
			&::before,
			&::after {
				right: -$clients-grid-border-size;
				left: auto;
				border-right: $clients-grid-border-size
					$clients-grid-border-style
					var(--#{$cnvs-prefix}contrast-400);
				border-left: 0;
			}
			&::after {
				right: 0;
				left: auto;
			}
		}
	}

	.testimonial {
		/* Testimonial - Pagination
		-----------------------------------------------------------------*/
		.flex-control-nav {
			left: 0;
			right: auto;
		}
	}
	.testi-image {
		float: right;
		margin-left: $testimonials-avatar-margin;
		margin-right: 0;
	}
	.testi-meta {
		span {
			padding-right: 10px;
			padding-left: 0;
		}
	}

	/* Twitter - Small Scroller
	-----------------------------------------------------------------*/
	.testimonial.twitter-scroll,
	.testimonial-full {
		.testi-meta {
			span {
				padding-right: 0;
				padding-left: 0;
			}
		}
	}

	/* ----------------------------------------------------------------
		Team
	-----------------------------------------------------------------*/

	.team {
		.si-share {
			text-align: right;
		}
		/* Team Center
		-----------------------------------------------------------------*/
		&.center {
			.team-title::after {
				right: 50%;
				left: auto;
				margin-right: -40px;
				margin-left: 0;
			}
		}
		/* Team List
		-----------------------------------------------------------------*/
		@at-root .team-list {
			.team-desc {
				text-align: right;
			}
		}
	}

	/* ----------------------------------------------------------------
		Pricing Boxes
	-----------------------------------------------------------------*/
	.pricing {
		/* Pricing Boxes - Price
		-----------------------------------------------------------------*/
		&-price {
			.price-unit {
				margin: 7px 0 0 3px;
			}
			.price-tenure {
				margin: 0 3px 0 0;
			}
		}

		/* Pricing Boxes - Extended
		-----------------------------------------------------------------*/
		@at-root .pricing-extended {
			text-align: right;
			.pricing-title {
				text-align: right;
			}
			.pricing-action-area {
				border-right: $pricing-title-border;
				border-left: 0;
			}
		}

		/* Pricing Boxes - 5 Columns
		-----------------------------------------------------------------*/
		&.pricing-5 {
			.pricing-box {
				float: right;
				&:nth-child(5) {
					margin-right: -$pricing-border-size;
					margin-left: 0;
				}
			}
		}
	}

	.table-comparison {
		& th:first-child,
		& td:first-child {
			text-align: right;
		}
	}

	/* ----------------------------------------------------------------
		Animated Rounded Skills
	-----------------------------------------------------------------*/

	.rounded-skill canvas {
		right: 0;
		left: auto;
	}

	/* ----------------------------------------------------------------
		Skills Bar
	-----------------------------------------------------------------*/

	.skills li .progress {
		right: 0;
		left: auto;
	}

	.skills li .progress-percent {
		left: 0;
		right: auto;
	}

	.progress-percent::after {
		right: 50%;
		left: auto;
		margin-right: -4px;
		margin-left: 0;
	}

	.progress-percent::before {
		right: 50%;
		left: auto;
		margin-right: -5px;
		margin-left: 0;
	}

	.skills li > span {
		right: 0;
		left: auto;
	}

	/* ----------------------------------------------------------------
		Quotes & Blockquotes
	-----------------------------------------------------------------*/

	blockquote {
		border-right: 5px solid #eee;
		border-left: 0;
	}

	.blockquote-reverse {
		padding-left: 15px;
		padding-right: 0;
		border-left: 5px solid #eee;
		border-right: 0;
		text-align: left;
	}

	blockquote.float-start {
		margin: 5px 0px 10px 20px;
		padding-left: 0;
	}

	blockquote.float-end {
		margin: 5px 20px 10px 0px;
		padding-right: 0;
	}

	.quote::before {
		right: 0;
		left: auto;
	}

	.quote.blockquote-reverse::before {
		left: auto;
		right: 0;
	}

	/* ----------------------------------------------------------------
		Dropcaps & Highlights
	-----------------------------------------------------------------*/

	.dropcap {
		float: right;
		margin: 0 0 0 5px;
	}

	/* ----------------------------------------------------------------
		Owl Carousel CSS
	-----------------------------------------------------------------*/
	.owl-carousel .owl-item {
		float: right;
	}

	.owl-carousel .owl-video-play-icon {
		right: 50%;
		left: auto;
		margin-right: -round($owl-video-play-icon-size / 2);
		margin-left: 0;
	}

	/* Owl Carousel - Controls - Arrows
	-----------------------------------------------------------------*/

	.owl-carousel .owl-nav [class*="owl-"] {
		right: -round($owl-nav-size * 2);
		left: auto;
	}

	.slider-element .owl-nav [class*="owl-"],
	.owl-carousel-full .owl-nav [class*="owl-"] {
		right: 0 !important;
		left: auto !important;
		border-radius: 0
			0
			$owl-full-nav-border-radius
			$owl-full-nav-border-radius;
	}

	.owl-carousel .owl-nav .owl-next {
		right: auto;
		left: -round($owl-nav-size * 2);
	}

	.slider-element .owl-nav .owl-next,
	.owl-carousel-full .owl-nav .owl-next {
		right: auto !important;
		left: 0 !important;
		border-radius: $owl-full-nav-border-radius
			$owl-full-nav-border-radius
			0
			0px;
	}

	.owl-carousel:hover .owl-nav [class*="owl-"] {
		right: -$owl-nav-size;
		left: auto;
	}

	.owl-carousel:hover .owl-nav .owl-next {
		right: auto;
		left: -$owl-nav-size;
	}

	/* ----------------------------------------------------------------
		Flip Cards
	-----------------------------------------------------------------*/

	.flip-card-front::after,
	.flip-card-back::after,
	.flip-card-back,
	.flip-card-inner,
	.bg-overlay,
	.bg-overlay-bg,
	.bg-overlay-content,
	#map-overlay .gmap {
		right: 0;
		left: auto;
	}

	/* ----------------------------------------------------------------
		Google Custom Search
	-----------------------------------------------------------------*/

	#content .gsc-result-info {
		padding-right: 0 !important;
	}

	/* ----------------------------------------------------------------
		Heading Styles
	-----------------------------------------------------------------*/

	/* Block Titles
	-----------------------------------------------------------------*/

	.title-block {
		padding: 2px $title-block-padding 3px 0px;
		border-right: $title-block-border solid $title-block-border-color;
		border-left: 0;
		text-align: right;
	}

	.title-block-right {
		padding: 2px 0px 3px $title-block-padding;
		border-right: 0;
		border-left: $title-block-border solid $title-block-border-color;
		text-align: left;
	}

	.fancy-title {
		&::before {
			margin-left: $fancy-title-padding;
			margin-right: 0;
		}

		&::after {
			margin-right: $fancy-title-padding;
			margin-left: 0;
		}
	}

	/* ----------------------------------------------------------------
		Divider
	-----------------------------------------------------------------*/

	.divider {
		&::before {
			margin-left: $divider-icon-gutter;
			margin-right: 0;
		}

		&::after {
			margin-right: $divider-icon-gutter;
			margin-left: 0;
		}
	}

	/* ----------------------------------------------------------------
		Magazine Specific Classes
	-----------------------------------------------------------------*/

	.bnews-title {
		float: right;
	}

	.bnews-slider {
		float: right;
		margin-right: 20px;
		margin-left: 0;
	}

	/* ----------------------------------------------------------------
		Go To Top
	-----------------------------------------------------------------*/

	#gotoTop {
		right: auto;
		left: $gotoTop-position-boxed-right;
		.stretched & {
			left: $gotoTop-position-boxed-right;
			right: auto;
		}
	}

	/* ----------------------------------------------------------------
		Read More
	-----------------------------------------------------------------*/

	.read-more-wrap .read-more-mask {
		right: 0;
		left: auto;
	}

	.read-more-trigger i {
		margin-right: 3px;
		margin-left: 0;
	}

	.read-more-wrap .read-more-trigger {
		right: 5px;
		left: auto;
	}

	/* ----------------------------------------------------------------
		GDPR Settings
	-----------------------------------------------------------------*/

	.gdpr-settings {
		right: 1rem;
		left: auto;
	}

	.gdpr-settings-sm {
		margin-left: 1rem;
		margin-right: 0;
	}

	.gdpr-settings-sm.gdpr-settings-right {
		right: auto;
		left: 1rem;
		margin-left: 0;
		margin-right: 1rem;
	}

	/* ----------------------------------------------------------------
		Landing Pages
	-----------------------------------------------------------------*/

	.landing-form-overlay {
		right: auto;
		left: 0;
		border-radius: 3px 0px 0 3px;
	}

	.landing-video {
		margin: 22px 95px 0 0px;
	}

	/* ----------------------------------------------------------------
		Preloaders
	-----------------------------------------------------------------*/

	.form-process {
		right: 0;
		left: auto;
	}

	/* ----------------------------------------------------------------
		Footer
	-----------------------------------------------------------------*/
	@if $footer-sticky-global {
		@include media-breakpoint-up(lg) {
			#footer {
				right: 0;
				left: auto;
			}
		}
	}

	@include media-breakpoint-up(lg) {
		.sticky-footer #footer {
			right: 0;
			left: auto;
		}
	}

	#copyrights {
		i.footer-icon {
			margin-left: 3px;
			margin-right: 0;
		}
		.text-end a:last-child {
			margin-left: 0;
			margin-right: 0;
		}
	}

	/* Tag Cloud
	-----------------------------------------------------------------*/

	.tagcloud a {
		float: right;
		margin-left: 4px;
		margin-right: 0;
	}

	/* ----------------------------------------------------------------
		Widgets
	-----------------------------------------------------------------*/

	.widget {
		&_nav_menu,
		&_links,
		&_meta,
		&_archive,
		&_recent_comments,
		&_recent_entries,
		&_categories,
		&_pages,
		&_rss {
			li {
				a {
					padding: 0 12px 0 3px;
				}
			}
			&:not(.widget-li-noicon) li::before {
				left: auto;
				right: 0;
			}
			&.widget-li-noicon li a {
				padding-right: 0;
			}
			ul ul {
				margin-right: 15px;
				margin-left: 0;
			}
		}

		/* Widget - Testimonial & Twitter
		-----------------------------------------------------------------*/

		.testimonial {
			&.twitter-scroll {
				.testi-image {
					margin-left: 10px;
					margin-right: 0;
				}
			}
		}
	}

	/* Twitter Feed Widget
	-----------------------------------------------------------------*/

	.widget-twitter-feed {
		.twitter-feed-avatar {
			margin-right: 44px;
			margin-left: 0;
			> li a.twitter-avatar {
				right: -44px;
				left: auto;
			}
		}
	}

	/* Widget Filter Links
	-----------------------------------------------------------------*/

	.widget-filter-links {
		.widget-filter-reset {
			right: auto;
			left: 0;
		}

		li {
			span {
				right: auto;
				left: 0;
			}
		}
	}

	/* Tag Cloud
	-----------------------------------------------------------------*/

	.tagcloud {
		a {
			float: right;
			margin-left: 4px;
			margin-right: 0px;
		}
	}

	/* Navigation Tree
	-----------------------------------------------------------------*/

	.nav-tree {
		ul {
			ul {
				a {
					padding-right: $nav-tree-padding-left;
					padding-left: 0;
				}
				ul a {
					padding-right: $nav-tree-padding-left + 20;
					padding-left: 0;
				}
				ul ul a {
					padding-right: $nav-tree-padding-left + 40;
					padding-left: 0;
				}
				ul ul ul a {
					padding-right: $nav-tree-padding-left + 60;
					padding-left: 0;
				}
			}
		}

		li {
			i {
				&:not(.icon-angle-down) {
					margin-right: 8px;
				}
				&.icon-angle-down {
					margin-left: 2px;
				}
			}
		}
	}

	/* ----------------------------------------------------------------
		Wedding
	-----------------------------------------------------------------*/

	.wedding-head {
		.first-name,
		.last-name,
		.and {
			margin-left: 15px;
			margin-right: 0;
			text-align: left;
		}

		.last-name {
			margin: 0 15px 0 0;
			text-align: right;
		}
	}

	/* ----------------------------------------------------------------
		Styled Paginations
	-----------------------------------------------------------------*/

	.pagination {
		/* Pagination Margins */
		&#{&}-circle .page-item:not(:first-child) .page-link,
		&#{&}-rounded .page-item:not(:first-child) .page-link {
			margin-right: 5px;
			margin-left: 0;
		}

		&#{&}-circle.pagination-lg .page-item:not(:first-child) .page-link,
		&#{&}-rounded.pagination-lg .page-item:not(:first-child) .page-link {
			margin-right: 8px;
			margin-left: 0;
		}

		&#{&}-circle.pagination-sm .page-item:not(:first-child) .page-link,
		&#{&}-rounded.pagination-sm .page-item:not(:first-child) .page-link {
			margin-right: 3px;
			margin-left: 0;
		}

		/* Pagination Inside Transparent */
		&#{&}-inside-transparent
			.page-item:not(:first-child):not(:nth-child(2)):not(:nth-last-child(1))
			.page-link {
			border-right: 0;
			border-left: 0;
		}

		/* Pagination Pill */
		&#{&}-pill .page-item:first-child .page-link {
			border-top-right-radius: 10rem;
			border-bottom-right-radius: 10rem;
			border-top-left-radius: 0;
			border-bottom-left-radius: 0;
		}

		&#{&}-pill .page-item:last-child .page-link {
			border-top-left-radius: 10rem;
			border-bottom-left-radius: 10rem;
			border-top-right-radius: 0;
			border-bottom-right-radius: 0;
		}
	}

	/* --------------------------------------------------------------
		SWITCH
	--------------------------------------------------------------  */

	.switch-toggle {
		margin-right: -9999px;
		margin-left: 0;
	}

	/* --------------------------------------------------------------
		SWITCH 1 - ROUND
	----------------------------------------------------------------- */
	input.switch-toggle-round + label::before,
	input.switch-toggle-round + label::after {
		right: 1px;
		left: auto;
	}

	input.switch-toggle-round + label::before {
		left: 1px;
		right: auto;
	}

	input.switch-toggle-round:checked + label::after {
		margin-right: $switch-toggle-size;
		margin-left: 0;
	}

	/* --------------------------------------------------------------
		SWITCH 1 - ROUND- MINI
	----------------------------------------------------------------- */
	input.switch-rounded-mini.switch-toggle-round:checked + label::after {
		margin-right: $switch-toggle-size-mini;
		margin-left: 0;
	}

	/* --------------------------------------------------------------
		SWITCH 1 - ROUND- LARGE
	----------------------------------------------------------------- */
	input.switch-rounded-large.switch-toggle-round:checked + label::after {
		margin-right: $switch-toggle-size-lg;
		margin-left: 0;
	}

	/* --------------------------------------------------------------
		SWITCH 1 - ROUND- XLARGE
	----------------------------------------------------------------- */
	input.switch-rounded-xlarge.switch-toggle-round:checked + label::after {
		margin-right: $switch-toggle-size-xl;
		margin-left: 0;
	}

	/* -----------------------------------------------------------
		SWITCH 2 - ROUND FLAT
	-------------------------------------------------------------- */
	input.switch-toggle-flat + label::after {
		right: 4px;
		left: auto;
	}
	input.switch-toggle-flat:checked + label::after {
		margin-right: 30px;
		margin-left: 0;
	}

	/* -----------------------------------------------------------
		SWITCH 2 - FLAT - MINI
	-------------------------------------------------------------- */

	input.switch-flat-mini.switch-toggle-flat + label::after {
		right: 2px;
		left: auto;
	}

	input.switch-toggle-flat:checked + label::after {
		margin-right: $switch-toggle-size;
		margin-left: 0;
	}

	input.switch-flat-mini.switch-toggle-flat:checked + label::after {
		margin-right: $switch-toggle-size-mini;
		margin-left: 0;
	}

	/* -----------------------------------------------------------
		SWITCH 2 - FLAT - LARGE
	-------------------------------------------------------------- */
	input.switch-flat-large.switch-toggle-flat:checked + label::after {
		margin-right: $switch-toggle-size-lg;
		margin-left: 0;
	}

	/* -----------------------------------------------------------
		SWITCH 2 - FLAT - XLARGE
	-------------------------------------------------------------- */

	input.switch-flat-xlarge.switch-toggle-flat:checked + label::after {
		margin-right: $switch-toggle-size-xl;
		margin-left: 0;
	}

	/* ----------------------------------------------------------------
		Bootstrap Specific
	-----------------------------------------------------------------*/

	.carousel-control .icon-chevron-left {
		right: 50%;
		left: auto;
	}

	.carousel-control .icon-chevron-right {
		left: 50%;
		right: auto;
	}

	label.radio,
	label.checkbox {
		padding-right: 20px;
		padding-left: 0;
	}

	/* Style-1 + Style-2 */
	.checkbox-style-1-label:before,
	.radio-style-1-label:before,
	.checkbox-style-2-label:before,
	.radio-style-2-label:before,
	.checkbox-style-3-label:before,
	.radio-style-3-label:before {
		margin-left: 10px;
		margin-right: 0;
	}

	/* Checkbox-small + Radio-small */
	.checkbox-style-1-label.checkbox-small:before,
	.radio-style-1-label.radio-small:before,
	.checkbox-style-2-label.checkbox-small:before,
	.radio-style-2-label.radio-small:before,
	.checkbox-style-3-label.checkbox-small:before,
	.radio-style-3-label.radio-small:before,
	.checkbox-style + .checkbox-style-3-label.checkbox-small:before,
	.radio-style + .radio-style-3-label.radio-small:before {
		margin: 0 0 1px 8px !important;
	}

	/* ----------------------------------------------------------------
		Cookie Notification
	-----------------------------------------------------------------*/

	#cookie-notification {
		right: 0;
		left: auto;
	}

	#cookie-notification .container {
		padding-left: 100px;
		padding-right: 0;
	}

	#cookie-notification .cookie-noti-btn {
		right: auto;
		left: 0;
	}

	/* ----------------------------------------------------------------
		Page Transitions
	-----------------------------------------------------------------*/

	.page-transition-wrap,
	.css3-spinner {
		right: 0;
		left: auto;
	}

	.css3-spinner > div {
		right: 50%;
		left: auto;
		margin-right: 13px;
		margin-left: 0;
	}

	.css3-spinner .css3-spinner-bounce1 {
		margin-right: -31px;
		margin-left: 0;
	}

	.css3-spinner .css3-spinner-bounce2 {
		margin-right: -9px;
		margin-left: 0;
	}

	.css3-spinner > .css3-spinner-flipper {
		margin-right: -16px;
		margin-left: 0;
	}

	.css3-spinner > .css3-spinner-double-bounce1,
	.css3-spinner > .css3-spinner-double-bounce2 {
		margin-right: -20px;
		margin-left: 0;
	}

	.css3-spinner > .css3-spinner-rect1,
	.css3-spinner > .css3-spinner-rect2,
	.css3-spinner > .css3-spinner-rect3,
	.css3-spinner > .css3-spinner-rect4,
	.css3-spinner > .css3-spinner-rect5 {
		margin-right: -21px;
		margin-left: 0;
	}

	.css3-spinner > .css3-spinner-rect2 {
		margin-right: -12px;
		margin-left: 0;
	}

	.css3-spinner > .css3-spinner-rect3 {
		margin-right: -3px;
		margin-left: 0;
	}

	.css3-spinner > .css3-spinner-rect4 {
		margin-right: 6px;
		margin-left: 0;
	}

	.css3-spinner > .css3-spinner-rect5 {
		margin-right: 15px;
		margin-left: 0;
	}

	.css3-spinner > .css3-spinner-cube1,
	.css3-spinner > .css3-spinner-cube2 {
		margin-right: -20px;
		margin-left: 0;
	}

	.css3-spinner > .css3-spinner-scaler {
		margin-right: -20px;
		margin-left: 0;
	}

	.css3-spinner > .css3-spinner-grid-pulse {
		margin-right: -24px;
		margin-left: 0;
	}

	.css3-spinner > .css3-spinner-clip-rotate {
		margin-right: -17px;
		margin-left: 0;
	}

	.css3-spinner > .css3-spinner-ball-rotate {
		margin-right: -6px;
		margin-left: 0;
	}

	.css3-spinner-ball-rotate > div:nth-child(1) {
		right: -22px;
		left: auto;
	}

	.css3-spinner-ball-rotate > div:nth-child(3) {
		right: 22px;
		left: auto;
	}

	.css3-spinner-zig-zag > div {
		margin-right: 15px;
		margin-left: 0;
		right: -7px;
		left: auto;
	}

	.css3-spinner > .css3-spinner-ball-scale-multiple {
		margin-right: -30px;
		margin-left: auto;
	}

	.css3-spinner-ball-scale-multiple > div {
		right: 0;
		left: auto;
	}

	.css3-spinner-triangle-path > div:nth-of-type(2) {
		right: 25px;
		left: auto;
	}

	.css3-spinner-triangle-path > div:nth-of-type(3) {
		right: 50px;
		left: auto;
	}
	.css3-spinner > .css3-spinner-ball-pulse-sync {
		margin-right: -35px;
		margin-left: 0;
	}

	.css3-spinner > .css3-spinner-scale-ripple {
		margin-right: -25px;
		margin-left: 0;
	}

	/* ----------------------------------------------------------------
		HTML5 Youtube Video Backgrounds
	-----------------------------------------------------------------*/

	.mb_YTVPlayer.fullscreen {
		right: 0 !important;
		left: auto !important;
	}

	.inline_YTPlayer {
		right: 0;
		left: auto;
	}

	/* ----------------------------------------------------------------
		font-icons.css
	-----------------------------------------------------------------*/

	.iconlist > li [class^="icon-"]:first-child,
	.iconlist > li [class*=" icon-"]:first-child {
		margin-left: 0.5rem;
		margin-right: 0;
	}

	.iconlist.iconlist-large > li [class^="icon-"],
	.iconlist.iconlist-large > li [class*=" icon-"] {
		margin-left: 5px;
		margin-right: 0;
	}

	.icon-stacked-1x,
	.icon-stacked-2x {
		left: auto;
		right: 0;
	}

	@if $enable-dark == true {
		/* ----------------------------------------------------------------

			Dark RTL

		`-----------------------------------------------------------------*/

		.dark .top-links-item,
		.dark #top-social li {
			border-right-color: rgba($white, 0.1);
			border-left-color: transparent;
		}

		@include media-breakpoint-up(lg) {
			.dark .full-header #logo,
			.full-header.dark #logo {
				border-left-color: rgba($white, 0.15);
				border-right-color: transparent;
			}

			.dark #header.transparent-header.full-header #logo,
			#header.transparent-header.full-header.dark #logo {
				border-left-color: rgba($white, 0.15);
				border-right-color: transparent;
			}
		}

		@include media-breakpoint-down(md) {
			/* Off Canvas Menu
			-----------------------------------------------------------------*/

			.dark
				#header:not(.sticky-header)
				.mobile-menu-off-canvas
				.menu-container,
			#header.dark:not(.sticky-header)
				.mobile-menu-off-canvas
				.menu-container {
				border-left-color: rgba($white, 0.2) !important;
				border-right-color: transparent !important;
			}

			.dark
				#header:not(.sticky-header)
				.mobile-menu-off-canvas.from-right
				.menu-container,
			#header.dark:not(.sticky-header)
				.mobile-menu-off-canvas.from-right
				.menu-container {
				border-right-color: rgba($white, 0.2) !important;
				border-left-color: transparent !important;
			}
		}

		@include media-breakpoint-up(lg) {
			.dark .full-header .primary-menu .menu-container,
			.full-header.dark .primary-menu .menu-container {
				border-left-color: rgba($white, 0.15);
				border-right-color: transparent;
			}

			.dark .transparent-header.full-header .primary-menu .menu-container,
			.transparent-header.full-header.dark .primary-menu .menu-container {
				border-left-color: rgba($white, 0.2);
				border-right-color: transparent;
			}

			.dark
				.primary-menu:not(.not-dark)
				.sub-menu-container.mega-menu-column:not(:first-child) {
				border-right-color: rgba($white, 0.05);
				border-left-color: transparent;
			}

			.dark .style-5 .menu-container {
				border-left-color: rgba($white, 0.15);
				border-right-color: transparent;
			}

			.side-header.dark #header,
			.side-header #header.dark {
				border-left-color: rgba($white, 0.15);
				border-right-color: transparent;
			}

			.side-header.side-header-right.dark #header,
			.side-header.side-header-right #header.dark {
				border-right-color: rgba($white, 0.15);
				border-left-color: transparent;
			}
		}

		.dark .top-cart-content {
			border-right-color: rgba($white, 0.1);
			border-left-color: transparent;
		}

		@include media-breakpoint-up(md) {
			.dark .grid-filter:not(.style-3) li a {
				border-right-color: rgba($white, 0.15);
				border-left-color: transparent;
			}
		}

		.dark .countdown-section {
			border-right-color: rgba($white, 0.15);
			border-left-color: transparent;
		}

		.dark .style-msg2 {
			border-right-color: rgba($black, 0.3);
			border-left-color: transparent;
		}

		.dark .style-msg .sb-msg,
		.dark .style-msg2 .msgtitle,
		.dark .style-msg2 .sb-msg {
			border-right-color: rgba($white, 0.1);
			border-left-color: transparent;
		}

		.dark .tab-nav li:first-child {
			border-right-color: rgba($white, 0.1);
			border-left-color: transparent;
		}
		@include media-breakpoint-up(md) {
			.dark .side-tabs .tab-nav {
				border-left-color: rgba($white, 0.1);
				border-right-color: transparent;
			}

			.dark .side-tabs .tab-nav li:first-child {
				border-right-color: rgba($white, 0.1);
				border-left-color: transparent;
			}

			.dark .tabs-bordered.side-tabs .tab-nav li.ui-tabs-active a {
				border-left-color: $body-bg-dark;
				border-right-color: transparent;
			}

			.dark .tabs-bordered.side-tabs .tab-container {
				border-right: 0;
				border-left: 0;
			}
		}

		.dark .clients-grid .grid-item::before,
		.dark .testimonials-grid .grid-item::before {
			border-right-color: rgba($white, 0.15);
			border-left-color: transparent;
		}

		.dark .pricing-extended .pricing-action-area {
			border-right-color: rgba($white, 0.15);
			border-left-color: transparent;
		}

		.dark .blockquote-reverse {
			border-left-color: rgba($white, 0.15);
			border-right-color: transparent;
		}

		.dark blockquote {
			border-right-color: rgba($white, 0.2);
			border-left-color: transparent;
		}

		.dark .blockquote-reverse,
		.dark blockquote.pull-right {
			border-left-color: rgba($white, 0.2);
			border-right-color: transparent;
		}

		.dark .popover.right > .arrow {
			border-left-color: lighten($body-bg-dark, 6.67);
			border-right-color: transparent;
		}

		.dark .popover.right > .arrow:after {
			border-left-color: $body-bg-darker;
			border-right-color: transparent;
		}

		.dark .popover.left > .arrow {
			border-right-color: lighten($body-bg-dark, 6.67);
			border-left-color: transparent;
		}

		.dark .popover.left > .arrow:after {
			border-right-color: $body-bg-darker;
			border-left-color: transparent;
		}
	}

	// colors.css
	.title-block {
		border-left-color: transparent;
		border-right-color: var(--#{$cnvs-prefix}themecolor);
	}

	.title-block-right {
		border-right-color: transparent;
		border-left-color: var(--#{$cnvs-prefix}themecolor);
	}

	.dots-menu .page-menu-item div::after {
		border-left-color: transparent;
		border-right-color: var(--#{$cnvs-prefix}themecolor);
	}
}
