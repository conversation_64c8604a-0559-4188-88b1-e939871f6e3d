<!DOCTYPE html>
<html dir="ltr" lang="en-US">
<head>

	<meta http-equiv="content-type" content="text/html; charset=utf-8">
	<meta http-equiv="x-ua-compatible" content="IE=edge">
	<meta name="author" content="SemiColonWeb">
	<meta name="description" content="Get Canvas to build powerful websites easily with the Highly Customizable &amp; Best Selling Bootstrap Template, today.">

	<!-- Font Imports -->
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:ital@0;1&display=swap" rel="stylesheet">

	<!-- Core Style -->
	<link rel="stylesheet" href="style.css">

	<!-- Font Icons -->
	<link rel="stylesheet" href="css/font-icons.css">

	<!-- Custom CSS -->
	<link rel="stylesheet" href="css/custom.css">
	<meta name="viewport" content="width=device-width, initial-scale=1">

	<!-- Document Title
	============================================= -->
	<title>Header - Full Menu | Canvas</title>

<style>
	#wrapper {
		transition: margin-top 0.3s ease-in-out;
	}

	.offcanvas-btn i {
		font-size: 1rem;
	}

	.offcanvas.offcanvas-top {
		position: absolute;
		height: max-content;

		background-color: var(--cnvs-contrast-100);
		--bs-offcanvas-border-color: rgba(var(--cnvs-contrast-rgb), .1);
	}

	body.offcanvas-open .offcanvas-btn i:nth-child(1),
	body:not(.offcanvas-open) .offcanvas-btn i:nth-child(2) {
		display: none;
	}

	.nav:not(.primary-nav) .nav-link {
		--bs-nav-link-padding-x: 0;
	}

	.primary-nav .nav-link {
		--bs-nav-link-padding-y: 8px;
		--bs-nav-link-font-size: 1.25rem;
		--bs-nav-link-font-weight: 700;
		--bs-nav-link-color: var(--cnvs-contrast-900);
	}

	.primary-nav .nav-link:hover {
		font-style: italic;
	}

	.offcanvas .container > .row > *:not(:first-child) {
		border-left: 1px solid var(--cnvs-contrast-200);
	}
</style>

</head>

<body class="stretched" data-menu-breakpoint="1200">

	<div class="offcanvas offcanvas-top dark py-lg-5 py-4" tabindex="-1" data-bs-backdrop="false" id="offcanvasMenu" aria-labelledby="offcanvasMenuLabel">
		<div class="container">
			<div class="row g-5">
				<div class="col-lg-4 col-6">
					<ul class="nav primary-nav flex-column">
						<li class="nav-item"><a class="nav-link" href="#"><div><i class="me-2 bi-house"></i> Home</div></a></li>
                        <li class="nav-item"><a class="nav-link" href="#"><div><i class="me-2 bi-person-circle"></i> About</div></a></li>
                        <li class="nav-item"><a class="nav-link" href="#"><div><i class="me-2 bi-pc-display"></i> Work</div></a></li>
                        <li class="nav-item"><a class="nav-link" href="#"><div><i class="me-2 bi-microsoft-teams"></i> Team</div></a></li>
                        <li class="nav-item"><a class="nav-link" href="#"><div><i class="me-2 bi-database"></i> Services</div></a></li>
                        <li class="nav-item"><a class="nav-link" href="#"><div><i class="me-2 bi-pen-fill"></i> Blogs</div></a></li>
					</ul>
				</div>
				<div class="col-lg-4 col-6">
					<h4>Helps & Supports</h4>
					<ul class="nav flex-column">
						<li class="nav-item"><a class="nav-link" href="#"><div><i class="me-2 bi-chat-dots-fill"></i> Forum Community</div></a></li>
                        <li class="nav-item"><a class="nav-link" href="#"><div><i class="me-2 bi-question-circle-fill"></i> FAQs</div></a></li>
                        <li class="nav-item"><a class="nav-link" href="#"><div><i class="me-2 bi-file-earmark-text-fill"></i> Documnentation</div></a></li>
                        <li class="nav-item"><a class="nav-link" href="#"><div><i class="me-2 bi-telephone-fill"></i> Contact Us</div></a></li>
					</ul>
				</div>

				<div class="col-lg-4 d-none d-lg-block border-start-0">
					<div class="card">
						<div class="card-body bg-contrast-200 p-4">
							<h3>Get In Touch</h3>
							<p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Libero consectetur deserunt magnam quasi! Explicabo nobis cupiditate accusamus doloribus deserunt! Nemo?</p>
							<a href="#"><u>Contact Now</u></a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Document Wrapper
	============================================= -->
	<div id="wrapper">

		<!-- Header
		============================================= -->
		<header id="header">
			<div id="header-wrap">
				<div class="container">
					<div class="header-row">

						<!-- Logo
						============================================= -->
						<div id="logo" class="me-5">
							<a href="index.html">
								<img class="logo-default" srcset="images/logo.png, images/<EMAIL> 2x" src="images/<EMAIL>" alt="Canvas Logo">
								<img class="logo-dark" srcset="images/logo-dark.png, images/<EMAIL> 2x" src="images/<EMAIL>" alt="Canvas Logo">
							</a>
						</div><!-- #logo end -->

						<div class="header-misc ms-auto">
                            <a data-bs-toggle="offcanvas" href="#offcanvasMenu" role="button" aria-controls="offcanvasMenu" class="offcanvas-btn button button-dark rounded-4 m-0"><i class="bi-list"></i> <i class="bi-x-lg"></i> <span>Menu</span></a>
                        </div>

					</div>
				</div>
			</div>
			<div class="header-wrap-clone"></div>
		</header><!-- #header end -->

		<!-- Content
		============================================= -->
		<section id="content">
			<div class="content-wrap">
				<div class="content-wrap text-center">
                    <a href="header-offcanvas-overlay.html" class="fs-4 link-dark link-opacity-75-hover"><u>Offcanvas - Overlay</u></a>
					<span class="mx-3">&middot;</span>
					<a href="header-offcanvas-light.html" class="fs-4 link-dark link-opacity-75-hover"><u>Offcanvas - Light</u></a>
				</div>
			</div>
		</section><!-- #content end -->

	</div><!-- #wrapper end -->

	<!-- JavaScripts
	============================================= -->
	<script src="js/plugins.min.js"></script>
	<script src="js/functions.bundle.js"></script>

	<script>
		window.addEventListener('load', function() {
			var offCanvas = document.querySelector("#offcanvasMenu"),
				offCanvasHeight = offCanvas.offsetHeight;

			offCanvas.addEventListener('show.bs.offcanvas', function() {
				SEMICOLON.Core.getVars.elBody.classList.add('offcanvas-open');
				SEMICOLON.Core.getVars.elWrapper.style.marginTop = offCanvasHeight + 'px';
			});

			offCanvas.addEventListener('hide.bs.offcanvas', function() {
				SEMICOLON.Core.getVars.elBody.classList.remove('offcanvas-open');
				SEMICOLON.Core.getVars.elWrapper.style.marginTop = 0;
			});
		});
    </script>

</body>
</html>