<!DOCTYPE html>
<html dir="ltr" lang="en-US">
<head>

	<meta http-equiv="content-type" content="text/html; charset=utf-8">
	<meta http-equiv="x-ua-compatible" content="IE=edge">
	<meta name="author" content="SemiColonWeb">
	<meta name="description" content="Get Canvas to build powerful websites easily with the Highly Customizable &amp; Best Selling Bootstrap Template, today.">

	<!-- Font Imports -->
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=PT+Serif:ital@0;1&display=swap" rel="stylesheet">

	<!-- Core Style -->
	<link rel="stylesheet" href="style.css">

	<!-- Font Icons -->
	<link rel="stylesheet" href="css/font-icons.css">

	<!-- Plugins/Components CSS -->
	<!-- SLIDER REVOLUTION 5.x CSS SETTINGS -->
	<link rel="stylesheet" href="include/rs-plugin/css/settings.css" media="screen">
	<link rel="stylesheet" href="include/rs-plugin/css/layers.css">
	<link rel="stylesheet" href="include/rs-plugin/css/navigation.css">

	<!-- Custom CSS -->
	<link rel="stylesheet" href="css/custom.css">
	<meta name="viewport" content="width=device-width, initial-scale=1">

	<!-- Document Title
	============================================= -->
	<title>Slider Fashion - Revolution Slider | Canvas</title>

	<style>
		.revo-slider-emphasis-text {
			font-size: 64px;
			font-weight: 700;
			letter-spacing: -1px;
			font-family: 'Poppins', sans-serif;
			padding: 15px 20px;
			border-top: 2px solid #FFF;
			border-bottom: 2px solid #FFF;
		}

		.revo-slider-desc-text {
			font-size: 20px;
			font-family: 'Lato', sans-serif;
			width: 650px;
			text-align: center;
			line-height: 1.5;
		}

		.revo-slider-caps-text {
			font-size: 16px;
			font-weight: 400;
			letter-spacing: 3px;
			font-family: 'Poppins', sans-serif;
		}
		.tp-video-play-button { display: none !important; }

		.tp-caption { white-space: nowrap; }
	</style>

</head>

<body class="stretched">

	<!-- Document Wrapper
	============================================= -->
	<div id="wrapper">

		<!-- Slider
		============================================= -->
		<section id="slider" class="slider-element revslider-wrap">

			<div id="rev_slider_10_1_wrapper" class="rev_slider_wrapper fullscreen-container" data-alias="fashion1" style="background-color:transparent;padding:0px;">
				<!-- START REVOLUTION SLIDER 5.0.7 fullscreen mode -->
				<div id="rev_slider_10_1" class="rev_slider fullscreenbanner" style="display:none;" data-version="5.0.7">
					<ul>	<!-- SLIDE  -->
						<li data-index="rs-34" data-transition="slidevertical" data-slotamount="7"  data-easein="default" data-easeout="default" data-masterspeed="1500"  data-thumb="include/rs-plugin/demos/assets/images/fashion_bg1-100x50.jpg"  data-rotate="0"  data-fstransition="fade" data-fsmasterspeed="1500" data-fsslotamount="7" data-saveperformance="off"  data-title="Intro" data-description="">
							<!-- MAIN IMAGE -->
							<img src="include/rs-plugin/demos/assets/images/fashion_bg1.jpg"  alt="Image"  data-bgposition="center center" data-bgfit="cover" data-bgrepeat="no-repeat" class="rev-slidebg" data-no-retina>
							<!-- LAYERS -->

							<!-- LAYER NR. 1 -->
							<div class="tp-caption   tp-resizeme"
								 id="slide-34-layer-1"
								 data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['0','0','0','0']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"

								 data-transform_in="z:0;rX:0deg;rY:0;rZ:0;sX:1.5;sY:1.5;skX:0;skY:0;opacity:0;s:1500;e:Power3.easeOut;"
								 data-transform_out="opacity:0;s:1000;s:1000;"
								 data-mask_in="x:0px;y:0px;s:inherit;e:inherit;"
								data-start="500"
								data-responsive_offset="on"


								style="z-index: 5;"><img src="include/rs-plugin/demos/assets/images/ellipse.png" alt="Image" width="350" height="350" data-ww="" data-hh="" data-no-retina>
							</div>

							<!-- LAYER NR. 3 -->
							<div class="tp-caption -   tp-resizeme"
								 id="slide-34-layer-3"
								 data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['0','0','0','0']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;rY:0;"

								 data-transform_in="rY:360deg;opacity:0;s:1500;e:Power2.easeInOut;"
								 data-transform_out="auto:auto;s:1000;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-responsive_offset="on"


								style="z-index: 7; white-space: nowrap; font-size: 120px; line-height: 120px; font-weight: 900; color: rgba(255, 255, 255, 1.00);font-family:Raleway;">C
							</div>

							<!-- LAYER NR. 4 -->
							<div class="tp-caption -   tp-resizeme"
								 id="slide-34-layer-4"
								 data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['68','68','68','68']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"

								 data-transform_in="opacity:0;s:1000;e:Power2.easeInOut;"
								 data-transform_out="auto:auto;s:1000;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-responsive_offset="on"


								style="z-index: 8; white-space: nowrap; font-size: 25px; line-height: 40px; font-weight: 400; color: rgba(255, 255, 255, 1.00);font-family:Crete Round;">Fashion
							</div>

							<!-- LAYER NR. 5 -->
							<div class="tp-caption -  "
								 id="slide-34-layer-5"
								 data-x="['right','right','right','right']" data-hoffset="['40','40','40','40']"
								 data-y="['bottom','bottom','bottom','bottom']" data-voffset="['40','40','40','40']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"
									data-style_hover="cursor:pointer;"

								 data-transform_in="y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="y:[100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;"
								 data-mask_out="x:inherit;y:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-actions='[{"event":"click","action":"jumptoslide","slide":"next","delay":""}]'
								data-basealign="slide"
								data-responsive_offset="off"
								data-responsive="off"

								style="z-index: 9; white-space: nowrap; font-size: 20px; line-height: 22px; font-weight: 400; color: rgba(255, 255, 255, 1.00);padding:3px 8px 3px 8px;border-color:rgba(255, 255, 255, 1.00);border-style:solid;border-width:1px;border-radius:30px 30px 30px 30px;"><i class="fa-solid fa-chevron-down"></i>
							</div>

							<!-- LAYER NR. 6 -->
							<div class="tp-caption Fashion-SmallText  "
								 id="slide-34-layer-6"
								 data-x="['right','right','right','right']" data-hoffset="['81','81','81','81']"
								 data-y="['bottom','bottom','bottom','bottom']" data-voffset="['45','45','45','45']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"
									data-style_hover="cursor:pointer;"

								 data-transform_in="y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="y:[100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;"
								 data-mask_out="x:inherit;y:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-actions='[{"event":"click","action":"jumptoslide","slide":"next","delay":""}]'
								data-basealign="slide"
								data-responsive_offset="off"
								data-responsive="off"

								style="z-index: 10; white-space: nowrap; color: rgba(255, 255, 255, 0.50);">OUR PHILOSOPHY
							</div>
						</li>
						<!-- SLIDE  -->
						<li data-index="rs-35" data-transition="slidevertical" data-slotamount="7"  data-easein="default" data-easeout="default" data-masterspeed="1500"   data-rotate="0"  data-saveperformance="off"  data-title="Philosophy" data-description="">
							<!-- MAIN IMAGE -->
							<img src="include/rs-plugin/demos/assets/images/transparent.png" style='background-color:#ffffff' alt="Image"  data-bgposition="center top" data-bgfit="contain" data-bgrepeat="no-repeat" class="rev-slidebg" data-no-retina>
							<!-- LAYERS -->

							<!-- LAYER NR. 1 -->
							<div class="tp-caption -  "
								 id="slide-35-layer-1"
								 data-x="['right','right','right','right']" data-hoffset="['40','40','40','40']"
								 data-y="['bottom','bottom','bottom','bottom']" data-voffset="['40','40','40','40']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"
									data-style_hover="cursor:pointer;"

								 data-transform_in="y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="y:[100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;"
								 data-mask_out="x:inherit;y:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-actions='[{"event":"click","action":"jumptoslide","slide":"next","delay":""}]'
								data-basealign="slide"
								data-responsive_offset="off"
								data-responsive="off"

								style="z-index: 5; white-space: nowrap; font-size: 20px; line-height: 22px; font-weight: 400; color: rgba(0, 0, 0, 1.00);padding:3px 8px 3px 8px;border-color:rgba(0, 0, 0, 1.00);border-style:solid;border-width:1px;border-radius:30px 30px 30px 30px;"><i class="fa-solid fa-chevron-down"></i>
							</div>

							<!-- LAYER NR. 2 -->
							<div class="tp-caption Fashion-SmallText  "
								 id="slide-35-layer-2"
								 data-x="['right','right','right','right']" data-hoffset="['83','83','83','83']"
								 data-y="['bottom','bottom','bottom','bottom']" data-voffset="['45','45','45','45']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"
									data-style_hover="cursor:pointer;"

								 data-transform_in="y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="y:[100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;"
								 data-mask_out="x:inherit;y:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-actions='[{"event":"click","action":"jumptoslide","slide":"next","delay":""}]'
								data-basealign="slide"
								data-responsive_offset="off"
								data-responsive="off"

								style="z-index: 6; white-space: nowrap; color: rgba(0, 0, 0, 0.50);">SUMMER LOOK (W)
							</div>

							<!-- LAYER NR. 3 -->
							<div class="tp-caption Fashion-BigDisplay   tp-resizeme"
								 id="slide-35-layer-3"
								 data-x="['center','center','center','center']" data-hoffset="['-320','-277','0','0']"
								 data-y="['top','top','top','top']" data-voffset="['451','432','452','422']"
											data-fontsize="['60','50','50','50']"
								data-lineheight="['60','70','70','70']"
								data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"

								 data-transform_in="x:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="x:[100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;s:inherit;e:inherit;"
								 data-mask_out="x:inherit;y:inherit;s:inherit;e:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-basealign="slide"
								data-responsive_offset="on"


								style="z-index: 7; white-space: nowrap;">BE INDIVIDUAL
							</div>

							<!-- LAYER NR. 4 -->
							<div class="tp-caption   tp-resizeme"
								 id="slide-35-layer-4"
								 data-x="['center','center','center','center']" data-hoffset="['0','0','0','0']"
								 data-y="['top','top','top','top']" data-voffset="['-259','-259','-259','-259']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"

								 data-transform_in="y:top;s:1500;e:Power2.easeInOut;"
								 data-transform_out="y:top;s:1500;e:Power2.easeInOut;s:1500;e:Power2.easeInOut;"
								data-start="500"
								data-basealign="slide"
								data-responsive_offset="on"


								style="z-index: 8;"><img src="include/rs-plugin/demos/assets/images/fashion_bg2_big.jpg" alt="Image" width="2304" height="648" data-ww="" data-hh="" data-no-retina>
							</div>

							<!-- LAYER NR. 5 -->
							<div class="tp-caption Fashion-TextBlock   tp-resizeme"
								 id="slide-35-layer-5"
								 data-x="['center','center','center','center']" data-hoffset="['289','237','0','0']"
								 data-y="['top','top','top','top']" data-voffset="['448','439','537','509']"
											data-fontsize="['20','17','17','15']"
								data-lineheight="['40','30','30','25']"
								data-width="['523','380','468','380']"
								data-height="['none','189','141','none']"
								data-whitespace="normal"
								data-transform_idle="o:1;"

								 data-transform_in="x:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="x:[-100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;s:inherit;e:inherit;"
								 data-mask_out="x:inherit;y:inherit;s:inherit;e:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-basealign="slide"
								data-responsive_offset="on"


								style="z-index: 9; min-width: 523px; max-width: 523px; white-space: normal;">If you want to be truly successful invest in
					yourself to get the knowledge you need to find your unique factor. When you find it and focus on it and persevere your success will blossom.
							</div>
						</li>
						<!-- SLIDE  -->
						<li data-index="rs-36" data-transition="slideoverhorizontal" data-slotamount="7"  data-easein="default" data-easeout="default" data-masterspeed="1500"   data-rotate="0"  data-saveperformance="off"  data-title="Summer Look" data-description="">
							<!-- MAIN IMAGE -->
							<img src="include/rs-plugin/demos/assets/images/transparent.png" style='background-color:#ffffff' alt="Image"  data-bgposition="center top" data-bgfit="contain" data-bgrepeat="no-repeat" class="rev-slidebg" data-no-retina>
							<!-- LAYERS -->

							<!-- LAYER NR. 1 -->
							<div class="tp-caption   tp-resizeme"
								 id="slide-36-layer-1"
								 data-x="['left','left','left','left']" data-hoffset="['0','-60','-10','-70']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['0','0','1','1']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"

								 data-transform_in="x:left;s:1500;e:Power2.easeInOut;"
								 data-transform_out="x:left;s:1500;e:Power2.easeInOut;s:1500;e:Power2.easeInOut;"
								data-start="500"
								data-basealign="slide"
								data-responsive_offset="on"


								style="z-index: 5;"><img src="include/rs-plugin/demos/assets/images/fashion_bg3.jpg" alt="Image" width="640" height="1080" data-ww="" data-hh="" data-no-retina>
							</div>

							<!-- LAYER NR. 2 -->
							<div class="tp-caption  "
								 id="slide-36-layer-2"
								 data-x="['left','left','left','left']" data-hoffset="['411','411','411','181']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['0','0','0','0']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-visibility="['off','off','on','on']"
								data-transform_idle="o:1;"

								 data-transform_in="x:right;s:1500;e:Power3.easeOut;"
								 data-transform_out="x:right;s:1000;e:Power3.easeIn;s:1000;e:Power3.easeIn;"
								data-start="500"
								data-basealign="slide"
								data-responsive_offset="on"
								data-responsive="off"

								style="z-index: 6;"><img src="include/rs-plugin/demos/assets/images/fashion_white.png" alt="Image" width="640" height="1080" data-ww="['390px','390px','390px','390px']" data-hh="['1080px','1080px','1080px','1080px']" data-no-retina>
							</div>

							<!-- LAYER NR. 3 -->
							<div class="tp-caption -  "
								 id="slide-36-layer-3"
								 data-x="['right','right','right','right']" data-hoffset="['40','40','40','40']"
								 data-y="['bottom','bottom','bottom','bottom']" data-voffset="['40','40','40','40']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"
									data-style_hover="cursor:pointer;"

								 data-transform_in="y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="y:[100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;"
								 data-mask_out="x:inherit;y:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-actions='[{"event":"click","action":"jumptoslide","slide":"next","delay":""}]'
								data-basealign="slide"
								data-responsive_offset="off"
								data-responsive="off"

								style="z-index: 7; white-space: nowrap; font-size: 20px; line-height: 22px; font-weight: 400; color: rgba(0, 0, 0, 1.00);padding:3px 8px 3px 8px;border-color:rgba(0, 0, 0, 1.00);border-style:solid;border-width:1px;border-radius:30px 30px 30px 30px;"><i class="fa-solid fa-chevron-down"></i>
							</div>

							<!-- LAYER NR. 4 -->
							<div class="tp-caption Fashion-SmallText  "
								 id="slide-36-layer-4"
								 data-x="['right','right','right','right']" data-hoffset="['83','83','83','83']"
								 data-y="['bottom','bottom','bottom','bottom']" data-voffset="['44','44','44','44']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"
									data-style_hover="cursor:pointer;"

								 data-transform_in="y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="y:[100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;"
								 data-mask_out="x:inherit;y:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-actions='[{"event":"click","action":"jumptoslide","slide":"next","delay":""}]'
								data-basealign="slide"
								data-responsive_offset="off"
								data-responsive="off"

								style="z-index: 8; white-space: nowrap; color: rgba(0, 0, 0, 0.50);">SMART LOOK (M)
							</div>

							<!-- LAYER NR. 5 -->
							<div class="tp-caption Fashion-BigDisplay   tp-resizeme"
								 id="slide-36-layer-5"
								 data-x="['center','center','center','center']" data-hoffset="['320','251','211','112']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['-40','-18','-18','-18']"
											data-fontsize="['60','50','50','50']"
								data-lineheight="['60','50','50','50']"
								data-width="['none','265','265','265']"
								data-height="['none','100','100','100']"
								data-whitespace="['nowrap','normal','normal','normal']"
								data-transform_idle="o:1;"

								 data-transform_in="x:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="x:[100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;s:inherit;e:inherit;"
								 data-mask_out="x:inherit;y:inherit;s:inherit;e:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-basealign="slide"
								data-responsive_offset="on"


								style="z-index: 9; white-space: nowrap;">SUMMER
					LOOK
							</div>

							<!-- LAYER NR. 6 -->
							<div class="tp-caption Fashion-TextBlock   tp-resizeme"
								 id="slide-36-layer-6"
								 data-x="['center','center','center','center']" data-hoffset="['199','229','189','89']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['130','150','150','150']"
											data-fontsize="['20','17','17','17']"
								data-lineheight="['40','30','30','30']"
								data-width="219"
								data-height="161"
								data-whitespace="normal"
								data-transform_idle="o:1;"

								 data-transform_in="x:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="x:[-100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;s:inherit;e:inherit;"
								 data-mask_out="x:inherit;y:inherit;s:inherit;e:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-basealign="slide"
								data-responsive_offset="on"


								style="z-index: 10; min-width: 219px; max-width: 219px; max-width: 161px; max-width: 161px; white-space: normal;">Black Top<br>
					Skirt with Pattern<br>
					Leather Boots<br>
					Accessoires

							</div>

							<!-- LAYER NR. 7 -->
							<div class="tp-caption Fashion-SmallText   tp-resizeme"
								 id="slide-36-layer-7"
								 data-x="['center','center','center','center']" data-hoffset="['120','150','109','9']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['-85','-85','-85','-85']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"

								 data-transform_in="y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="y:[100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;s:inherit;e:inherit;"
								 data-mask_out="x:inherit;y:inherit;s:inherit;e:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-basealign="slide"
								data-responsive_offset="on"


								style="z-index: 11; white-space: nowrap; color: rgba(0, 0, 0, 1.00);">WOMEN
							</div>

							<!-- LAYER NR. 8 -->
							<div class="tp-caption Fashion-TextBlock   tp-resizeme"
								 id="slide-36-layer-8"
								 data-x="['center','center','center','center']" data-hoffset="['424','454','384','284']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['130','150','150','150']"
											data-fontsize="['20','17','17','17']"
								data-lineheight="['40','30','30','30']"
								data-width="219"
								data-height="161"
								data-whitespace="normal"
								data-transform_idle="o:1;"

								 data-transform_in="x:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="x:[100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;s:inherit;e:inherit;"
								 data-mask_out="x:inherit;y:inherit;s:inherit;e:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-basealign="slide"
								data-responsive_offset="on"


								style="z-index: 12; min-width: 219px; max-width: 219px; max-width: 161px; max-width: 161px; white-space: normal; font-weight: 600;">$ 35<br>
					$ 69<br>
					$ 120<br>
					$ 14
							</div>
						</li>
						<!-- SLIDE  -->
						<li data-index="rs-37" data-transition="slideoverhorizontal" data-slotamount="7"  data-easein="default" data-easeout="default" data-masterspeed="1500"   data-rotate="0"  data-saveperformance="off"  data-title="Smart Look" data-description="">
							<!-- MAIN IMAGE -->
							<img src="include/rs-plugin/demos/assets/images/transparent.png" style='background-color:#ffffff' alt="Image"  data-bgposition="center top" data-bgfit="contain" data-bgrepeat="no-repeat" class="rev-slidebg" data-no-retina>
							<!-- LAYERS -->

							<!-- LAYER NR. 1 -->
							<div class="tp-caption   tp-resizeme"
								 id="slide-37-layer-1"
								 data-x="['left','left','left','left']" data-hoffset="['0','-60','-110','-260']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['0','0','1','1']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"

								 data-transform_in="x:left;s:1500;e:Power2.easeInOut;"
								 data-transform_out="x:left;s:1500;e:Power2.easeInOut;s:1500;e:Power2.easeInOut;"
								data-start="500"
								data-basealign="slide"
								data-responsive_offset="on"


								style="z-index: 5;"><img src="include/rs-plugin/demos/assets/images/fashion_bg4.jpg" alt="Image" width="640" height="1080" data-ww="" data-hh="" data-no-retina>
							</div>

							<!-- LAYER NR. 2 -->
							<div class="tp-caption  "
								 id="slide-37-layer-2"
								 data-x="['left','left','left','left']" data-hoffset="['411','411','411','181']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['0','0','0','0']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-visibility="['off','off','on','on']"
								data-transform_idle="o:1;"

								 data-transform_in="x:right;s:1500;e:Power3.easeOut;"
								 data-transform_out="x:right;s:1000;e:Power3.easeIn;s:1000;e:Power3.easeIn;"
								data-start="500"
								data-basealign="slide"
								data-responsive_offset="on"
								data-responsive="off"

								style="z-index: 6;"><img src="include/rs-plugin/demos/assets/images/fashion_white.png" alt="Image" width="640" height="1080" data-ww="['390px','390px','390px','390px']" data-hh="['1080px','1080px','1080px','1080px']" data-no-retina>
							</div>

							<!-- LAYER NR. 3 -->
							<div class="tp-caption -  "
								 id="slide-37-layer-3"
								 data-x="['right','right','right','right']" data-hoffset="['40','40','40','40']"
								 data-y="['bottom','bottom','bottom','bottom']" data-voffset="['40','40','40','40']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"
									data-style_hover="cursor:pointer;"

								 data-transform_in="y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="y:[100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;"
								 data-mask_out="x:inherit;y:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-actions='[{"event":"click","action":"jumptoslide","slide":"next","delay":""}]'
								data-basealign="slide"
								data-responsive_offset="off"
								data-responsive="off"

								style="z-index: 7; white-space: nowrap; font-size: 20px; line-height: 22px; font-weight: 400; color: rgba(0, 0, 0, 1.00);padding:3px 8px 3px 8px;border-color:rgba(0, 0, 0, 1.00);border-style:solid;border-width:1px;border-radius:30px 30px 30px 30px;"><i class="fa-solid fa-chevron-down"></i>
							</div>

							<!-- LAYER NR. 4 -->
							<div class="tp-caption Fashion-SmallText  "
								 id="slide-37-layer-4"
								 data-x="['right','right','right','right']" data-hoffset="['83','83','83','83']"
								 data-y="['bottom','bottom','bottom','bottom']" data-voffset="['44','44','44','44']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"
									data-style_hover="cursor:pointer;"

								 data-transform_in="y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="y:[100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;"
								 data-mask_out="x:inherit;y:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-actions='[{"event":"click","action":"jumptoslide","slide":"next","delay":""}]'
								data-basealign="slide"
								data-responsive_offset="off"
								data-responsive="off"

								style="z-index: 8; white-space: nowrap; color: rgba(0, 0, 0, 0.50);">FIND US
							</div>

							<!-- LAYER NR. 5 -->
							<div class="tp-caption Fashion-BigDisplay   tp-resizeme"
								 id="slide-37-layer-5"
								 data-x="['center','center','center','center']" data-hoffset="['290','251','211','112']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['-40','-18','-18','-18']"
											data-fontsize="['60','50','50','50']"
								data-lineheight="['60','50','50','50']"
								data-width="['none','265','265','265']"
								data-height="['none','100','100','100']"
								data-whitespace="['nowrap','normal','normal','normal']"
								data-transform_idle="o:1;"

								 data-transform_in="x:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="x:[100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;s:inherit;e:inherit;"
								 data-mask_out="x:inherit;y:inherit;s:inherit;e:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-basealign="slide"
								data-responsive_offset="on"


								style="z-index: 9; white-space: nowrap;">SMART LOOK
							</div>

							<!-- LAYER NR. 6 -->
							<div class="tp-caption Fashion-TextBlock   tp-resizeme"
								 id="slide-37-layer-6"
								 data-x="['center','center','center','center']" data-hoffset="['199','229','189','89']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['130','150','150','150']"
											data-fontsize="['20','17','17','17']"
								data-lineheight="['40','30','30','30']"
								data-width="219"
								data-height="161"
								data-whitespace="normal"
								data-transform_idle="o:1;"

								 data-transform_in="x:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="x:[-100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;s:inherit;e:inherit;"
								 data-mask_out="x:inherit;y:inherit;s:inherit;e:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-basealign="slide"
								data-responsive_offset="on"


								style="z-index: 10; min-width: 219px; max-width: 219px; max-width: 161px; max-width: 161px; white-space: normal;">Tank Top<br>
					Shirt<br>
					Shorts<br>
					Swag

							</div>

							<!-- LAYER NR. 7 -->
							<div class="tp-caption Fashion-SmallText   tp-resizeme"
								 id="slide-37-layer-7"
								 data-x="['center','center','center','center']" data-hoffset="['110','139','109','9']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['-85','-85','-85','-85']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"

								 data-transform_in="y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="y:[100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;s:inherit;e:inherit;"
								 data-mask_out="x:inherit;y:inherit;s:inherit;e:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-basealign="slide"
								data-responsive_offset="on"


								style="z-index: 11; white-space: nowrap; color: rgba(0, 0, 0, 1.00);">MENS
							</div>

							<!-- LAYER NR. 8 -->
							<div class="tp-caption Fashion-TextBlock   tp-resizeme"
								 id="slide-37-layer-8"
								 data-x="['center','center','center','center']" data-hoffset="['424','454','384','284']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['130','150','150','150']"
											data-fontsize="['20','17','17','17']"
								data-lineheight="['40','30','30','30']"
								data-width="219"
								data-height="161"
								data-whitespace="normal"
								data-transform_idle="o:1;"

								 data-transform_in="x:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
								 data-transform_out="x:[100%];s:1000;e:Power3.easeInOut;s:1000;e:Power3.easeInOut;"
								 data-mask_in="x:0px;y:0px;s:inherit;e:inherit;"
								 data-mask_out="x:inherit;y:inherit;s:inherit;e:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-basealign="slide"
								data-responsive_offset="on"


								style="z-index: 12; min-width: 219px; max-width: 219px; max-width: 161px; max-width: 161px; white-space: normal; font-weight: 600;">$ 19<br>
					$ 35<br>
					$ 60<br>
					-
							</div>
						</li>
						<!-- SLIDE  -->
						<li data-index="rs-38" data-transition="slideoververtical" data-slotamount="7"  data-easein="default" data-easeout="default" data-masterspeed="1500"  data-thumb="include/rs-plugin/demos/assets/images/girls-100x50.jpg"  data-rotate="0"  data-saveperformance="off"  data-title="Slide" data-description="">
							<!-- MAIN IMAGE -->
							<img src="include/rs-plugin/demos/assets/images/girls.jpg"  alt="Image"  data-bgposition="center center" data-bgfit="cover" data-bgrepeat="no-repeat" class="rev-slidebg" data-no-retina>
							<!-- LAYERS -->

							<!-- BACKGROUND VIDEO LAYER -->
							<div class="rs-background-video-layer"
								data-forcerewind="on"
								data-volume="mute"
								data-videowidth="100%"
								data-videoheight="100%"
								data-videomp4="include/rs-plugin/demos/assets/videos/girls.mp4"
								data-videopreload="preload"
								data-videoloop="none"
								data-forceCover="1"
								data-aspectratio="16:9"
								data-autoplay="true"
								data-autoplayonlyfirsttime="false"
								data-nextslideatend="true"
							></div>
							<!-- LAYER NR. 1 -->
							<div class="tp-caption -  "
								 id="slide-38-layer-1"
								 data-x="['left','left','left','left']" data-hoffset="['0','0','0','0']"
								 data-y="['top','top','top','top']" data-voffset="['0','0','0','0']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"

								 data-transform_in="opacity:0;s:1500;e:Power2.easeOut;"
								 data-transform_out="auto:auto;s:1500;e:Power2.easeOut;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-basealign="slide"
								data-responsive_offset="off"
								data-responsive="off"

								style="z-index: 5; white-space: nowrap; font-size: 20px; line-height: 22px; font-weight: 400; color: rgba(255, 255, 255, 1.00);"><div class="coverdark"></div>
							</div>

							<!-- LAYER NR. 2 -->
							<div class="tp-caption Fashion-BigDisplay   tp-resizeme"
								 id="slide-38-layer-2"
								 data-x="['left','center','center','center']" data-hoffset="['40','0','0','0']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['0','49','146','21']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"

								 data-transform_in="y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;s:2000;e:Power4.easeInOut;"
								 data-transform_out="y:[100%];s:1500;e:Power2.easeInOut;s:1500;e:Power2.easeInOut;"
								 data-mask_in="x:0px;y:[100%];s:inherit;e:inherit;"
								 data-mask_out="x:inherit;y:inherit;s:inherit;e:inherit;"
								data-start="500"
								data-splitin="none"
								data-splitout="none"
								data-responsive_offset="on"


								style="z-index: 6; white-space: nowrap; color: rgba(255, 255, 255, 1.00);">EZ LIFESTYLE
							</div>

							<!-- LAYER NR. 3 -->
							<div class="tp-caption Fashion-BigDisplay   tp-resizeme"
								 id="slide-38-layer-3"
								 data-x="['left','center','center','center']" data-hoffset="['40','0','0','0']"
								 data-y="['middle','middle','middle','middle']" data-voffset="['52','101','198','74']"
											data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"

								 data-transform_in="y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;s:2000;e:Power4.easeInOut;"
								 data-transform_out="y:[100%];s:1500;e:Power2.easeInOut;s:1500;e:Power2.easeInOut;"
								 data-mask_in="x:0px;y:[100%];s:inherit;e:inherit;"
								 data-mask_out="x:inherit;y:inherit;s:inherit;e:inherit;"
								data-start="750"
								data-splitin="none"
								data-splitout="none"
								data-responsive_offset="on"


								style="z-index: 7; white-space: nowrap; font-size: 30px; line-height: 30px; font-weight: 400; color: rgba(255, 255, 255, 1.00);">VISIT OUR STORE
							</div>

							<!-- LAYER NR. 4 -->
							<div class="tp-caption Fashion-TextBlock   tp-resizeme"
								 id="slide-38-layer-4"
								 data-x="['right','center','center','center']" data-hoffset="['40','0','0','0']"
								 data-y="['middle','bottom','bottom','middle']" data-voffset="['46','80','80','201']"
											data-fontsize="['20','20','20','15']"
								data-lineheight="['40','30','30','20']"
								data-fontweight="['400','300','300','400']"
								data-width="none"
								data-height="none"
								data-whitespace="nowrap"
								data-transform_idle="o:1;"

								 data-transform_in="y:[100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;opacity:0;s:2000;e:Power4.easeInOut;"
								 data-transform_out="y:[100%];s:1500;e:Power2.easeInOut;s:1500;e:Power2.easeInOut;"
								 data-mask_in="x:0px;y:[100%];s:inherit;e:inherit;"
								 data-mask_out="x:inherit;y:inherit;s:inherit;e:inherit;"
								data-start="1000"
								data-splitin="none"
								data-splitout="none"
								data-responsive_offset="on"


								style="z-index: 8; white-space: nowrap; color: rgba(255, 255, 255, 1.00);"><i class="bi-geo-alt-fill" style="color:rgba(255,255,255,0.35);"></i> Shop Street 234, LA<br>
					<i class="bi-telephone" style="color:rgba(255,255,255,0.35);"></i> 0800 987654<br>
					<i class="bi-envelope" style="color:rgba(255,255,255,0.35);"></i> <EMAIL><br>
					<i class="bi-clock"  style="color:rgba(255,255,255,0.35);"></i> Monday - Saturday 07:30 - 22:30
							</div>
						</li>
					</ul>
					<div class="tp-static-layers">

						<!-- LAYER NR. 1 -->
						<div class="tp-caption   tp-static-layer"
							 id="slide-6-layer-1"
							 data-x="['left','left','left','left']" data-hoffset="['40','40','40','40']"
							 data-y="['top','top','top','top']" data-voffset="['0','0','0','0']"
										data-width="none"
							data-height="none"
							data-whitespace="nowrap"
							data-transform_idle="o:1;"

							 data-transform_in="y:[-100%];z:0;rX:0deg;rY:0;rZ:0;sX:1;sY:1;skX:0;skY:0;s:1500;e:Power3.easeInOut;"
							 data-transform_out="y:[100%];s:1000;s:1000;"
							 data-mask_in="x:0px;y:0px;"
							 data-mask_out="x:inherit;y:inherit;"
							data-start="500"
							data-basealign="slide"
							data-responsive_offset="off"
							data-responsive="off"
							data-startslide="0"
							data-endslide="4"

							style="z-index: 5;"><img src="images/<EMAIL>" alt="Image" width="62" height="100" data-ww="['126px','126px','126px','126px']" data-hh="['100px','100px','100px','100px']" data-no-retina>
						</div>
					</div>
					<div class="tp-bannertimer tp-bottom" style="visibility: hidden !important;"></div>
				</div>
			</div><!-- END REVOLUTION SLIDER -->

		</section><!-- #content end -->

	</div><!-- #wrapper end -->

	<!-- JavaScripts
	============================================= -->
	<script src="js/plugins.min.js"></script>
	<script src="js/functions.bundle.js"></script>

	<!-- SLIDER REVOLUTION 5.x SCRIPTS  -->
	<script src="include/rs-plugin/js/jquery.themepunch.tools.min.js"></script>
	<script src="include/rs-plugin/js/jquery.themepunch.revolution.min.js"></script>

	<script src="include/rs-plugin/js/extensions/revolution.extension.video.min.js"></script>
	<script src="include/rs-plugin/js/extensions/revolution.extension.slideanims.min.js"></script>
	<script src="include/rs-plugin/js/extensions/revolution.extension.actions.min.js"></script>
	<script src="include/rs-plugin/js/extensions/revolution.extension.layeranimation.min.js"></script>
	<script src="include/rs-plugin/js/extensions/revolution.extension.navigation.min.js"></script>

	<script>
		var tpj=jQuery;
		var revapi10;

		tpj(document).ready(function() {
			if(tpj("#rev_slider_10_1").revolution == undefined){
				revslider_showDoubleJqueryError("#rev_slider_10_1");
			}else{
				revapi10 = tpj("#rev_slider_10_1").show().revolution({
					sliderType:"standard",
					jsFileLocation:"include/rs-plugin/js/",
					sliderLayout:"fullscreen",
					dottedOverlay:"none",
					delay:9000,
					navigation: {
						keyboardNavigation:"on",
						keyboard_direction: "horizontal",
						mouseScrollNavigation:"on",
						onHoverStop:"off",
						touch:{
							touchenabled:"on",
							swipe_threshold: 75,
							swipe_min_touches: 1,
							swipe_direction: "vertical",
							drag_block_vertical: false
						},
						bullets: {
							enable:true,
							hide_onmobile:false,
							style:"uranus",
							hide_onleave:false,
							direction:"vertical",
							h_align:"left",
							v_align:"center",
							h_offset:30,
							v_offset:0,
							space:5,
							tmp:'<span class="tp-bullet-inner"></span>'
						}
					},
					responsiveLevels:[1240,1024,778,480],
					gridwidth:[1240,1024,778,480],
					gridheight:[868,768,960,720],
					lazyType:"none",
					shadow:0,
					spinner:"off",
					stopLoop:"on",
					stopAfterLoops:0,
					stopAtSlide:1,
					shuffle:"off",
					autoHeight:"off",
					fullScreenAlignForce:"off",
					fullScreenOffsetContainer: "",
					fullScreenOffset: "",
					disableProgressBar:"on",
					hideThumbsOnMobile:"off",
					hideSliderAtLimit:0,
					hideCaptionAtLimit:0,
					hideAllCaptionAtLilmit:0,
					debugMode:false,
					fallbacks: {
						simplifyAll:"off",
						nextSlideOnWindowFocus:"off",
						disableFocusListener:false,
					}
				});
			}
		});	/*ready*/
	</script>

</body>
</html>