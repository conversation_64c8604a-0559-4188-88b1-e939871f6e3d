<!DOCTYPE html>
<html class="no-js" lang="zxx">
<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>标签云 - 重庆锦雨丰建筑有限公司</title>
    <meta name="description" content="浏览重庆锦雨丰建筑有限公司的标签云，快速找到您关注的产品和服务分类。">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.png">

    <!-- CSS
    ========================= -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/fontawesome.min.css">
    <link rel="stylesheet" href="assets/css/magnific-popup.min.css">
    <link rel="stylesheet" href="assets/css/swiper-bundle.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- 专业标签云样式 -->
    <style>
        /* 专业标签云头部样式 */
        .professional-tagcloud-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 60px 30px;
            border-radius: 20px;
            border: 1px solid #dee2e6;
        }
        
        .section-title-wrapper .sub-title {
            color: var(--theme-color);
            font-weight: 600;
            letter-spacing: 2px;
            margin-bottom: 15px;
        }
        
        /* 统计卡片样式 */
        .tagcloud-stats-container {
            margin-top: 40px;
        }
        
        .stats-card {
            background: white;
            padding: 30px 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
            height: 100%;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .stats-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, var(--theme-color), #0d4aad);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            color: white;
            font-size: 28px;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--title-color);
            margin: 20px 0 10px;
        }
        
        .stats-label {
            color: #6c757d;
            font-weight: 500;
            font-size: 1.1rem;
        }
        
        /* 专业标签云区域样式 */
        .professional-tagcloud-section {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            border: 1px solid #f0f0f0;
        }
        
        .section-header {
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 20px;
        }
        
        .section-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--title-color);
            margin-bottom: 8px;
        }
        
        .section-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }
        
        .tag-count-badge {
            background: var(--theme-color);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        /* 核心产品标签样式 */
        .tagcloud-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }
        
        .professional-tag {
            display: flex;
            align-items: center;
            padding: 25px;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .professional-tag:hover {
            border-color: var(--theme-color);
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(31, 75, 194, 0.15);
            text-decoration: none;
        }
        
        .professional-tag.primary-tag {
            background: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);
        }
        
        .professional-tag.secondary-tag {
            background: linear-gradient(135deg, #fff 0%, #f8faf8 100%);
        }
        
        .tag-icon {
            width: 50px;
            height: 50px;
            background: var(--theme-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-right: 20px;
            flex-shrink: 0;
        }
        
        .tag-text {
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--title-color);
            flex: 1;
        }
        
        .tag-badge {
            background: var(--theme-color);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
            position: absolute;
            top: 12px;
            right: 12px;
        }
        
        /* 技术特色标签样式 */
        .feature-tagcloud {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-tag {
            display: block;
            padding: 20px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 4px solid var(--theme-color);
        }
        
        .feature-tag:hover {
            background: #f8f9fa;
            transform: translateX(5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            text-decoration: none;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            background: var(--theme-color);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            margin-bottom: 15px;
        }
        
        .feature-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--title-color);
            margin-bottom: 5px;
        }
        
        .feature-desc {
            color: #6c757d;
            font-size: 0.9rem;
            margin: 0;
        }
        
        /* 细分产品标签样式 */
        .detailed-tagcloud {
            margin-top: 30px;
        }
        
        .tag-category-group {
            margin-bottom: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 5px solid var(--theme-color);
        }
        
        .category-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--title-color);
            margin-bottom: 20px;
        }
        
        .category-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }
        
        .detail-tag {
            background: white;
            color: var(--title-color);
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: 500;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }
        
        .detail-tag:hover {
            background: var(--theme-color);
            color: white;
            border-color: var(--theme-color);
            text-decoration: none;
            transform: translateY(-2px);
        }
        
        /* 应用场景卡片样式 */
        .application-card {
            display: block;
            padding: 25px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            text-align: center;
            height: 100%;
        }
        
        .application-card:hover {
            border-color: var(--theme-color);
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(31, 75, 194, 0.15);
            text-decoration: none;
        }
        
        .app-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--theme-color), #0d4aad);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin: 0 auto 20px;
        }
        
        .app-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--title-color);
            margin-bottom: 10px;
        }
        
        .app-count {
            color: #6c757d;
            font-size: 0.9rem;
            margin: 0;
            font-weight: 500;
        }
        
        /* 专业服务说明样式 */
        .professional-service-note {
            background: linear-gradient(135deg, var(--theme-color), #0d4aad);
            color: white;
            padding: 40px;
            border-radius: 15px;
            margin-top: 40px;
        }
        
        .note-title {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .note-text {
            color: rgba(255,255,255,0.9);
            font-size: 1.1rem;
            line-height: 1.6;
            margin: 0;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .professional-tagcloud-header {
                padding: 40px 20px;
            }
            
            .professional-tagcloud-section {
                padding: 25px 20px;
            }
            
            .tagcloud-grid {
                grid-template-columns: 1fr;
            }
            
            .feature-tagcloud {
                grid-template-columns: 1fr;
            }
            
            .professional-tag {
                padding: 20px;
            }
            
            .tag-text {
                font-size: 1.2rem;
            }
            
            .stats-number {
                font-size: 2rem;
            }
        }
    </style>

</head>

<body>

    <!--[if lte IE 9]>
        <p class="browserupgrade">You are using an <strong>outdated</strong> browser. Please <a href="https://browsehappy.com/">upgrade your browser</a> to improve your experience and security.</p>
    <![endif]-->


    <!--********************************
			Code Start From Here 
	********************************-->




    <!--==============================
     Preloader
  ==============================-->
    <div class="preloader ">
        <button class="th-btn preloaderCls">取消</button>
        <div class="preloader-inner">
            <div class="loader"></div>
        </div>
    </div>
    <!--==============================
    Sidemenu
============================== -->
    <div class="sidemenu-wrapper">
        <div class="sidemenu-content">
            <button class="closeButton sideMenuCls"><i class="far fa-times"></i></button>
            <div class="widget woocommerce widget_shopping_cart">
                <div class="widget-top">
                    <h3 class="widget_title">购物车</h3>
                </div>
            </div>
            <div class="multi-layered-slider">
                <div class="slider-nav">
                    <div class="hero-inner">
                        <h1 class="hero-title">
                            <a href="blog.html">建筑装饰</a>
                        </h1>
                        <p class="hero-text">专业的建筑幕墙和门窗解决方案提供商</p>
                        <div class="btn-group">
                            <a href="contact.html" class="th-btn">联系我们<i class="fas fa-arrow-right ms-2"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--==============================
    Header Area
  ==============================-->
    <header class="th-header header-default">
        <div class="header-top">
            <div class="container">
                <div class="row justify-content-center justify-content-lg-between align-items-center gy-2">
                    <div class="col-auto d-none d-lg-block">
                        <div class="header-links">
                            <ul>
                                <li><i class="far fa-phone"></i><a href="tel:+02340081222">023-4008-1222</a></li>
                                <li><i class="far fa-envelope"></i><a href="mailto:<EMAIL>"><EMAIL></a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="header-links">
                            <ul>
                                <li>
                                    <div class="social-links">
                                        <a href="https://www.facebook.com/"><i class="fab fa-facebook-f"></i></a>
                                        <a href="https://www.twitter.com/"><i class="fab fa-twitter"></i></a>
                                        <a href="https://www.linkedin.com/"><i class="fab fa-linkedin-in"></i></a>
                                        <a href="https://www.instagram.com/"><i class="fab fa-instagram"></i></a>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="sticky-wrapper">
            <div class="menu-area">
                <div class="container">
                    <div class="row align-items-center justify-content-between">
                        <div class="col-auto">
                            <div class="header-logo">
                                <a href="home.html"><img src="assets/img/logo.svg" alt="重庆锦雨丰建筑"></a>
                            </div>
                        </div>
                        <div class="col-auto">
                            <nav class="main-menu d-none d-lg-inline-block">
                                <ul>
                                    <li><a href="home.html">首页</a></li>
                                    <li><a href="about.html">关于我们</a></li>
                                    <li class="menu-item-has-children">
                                        <a href="#">产品服务</a>
                                        <ul class="sub-menu">
                                            <li><a href="service.html">玻璃幕墙</a></li>
                                            <li><a href="service-details.html">铝合金门窗</a></li>
                                            <li><a href="service.html">钢结构工程</a></li>
                                            <li><a href="service-details.html">装饰装修</a></li>
                                        </ul>
                                    </li>
                                    <li class="menu-item-has-children">
                                        <a href="#">案例展示</a>
                                        <ul class="sub-menu">
                                            <li><a href="project.html">工程案例</a></li>
                                            <li><a href="project-details.html">案例详情</a></li>
                                        </ul>
                                    </li>
                                    <li class="menu-item-has-children">
                                        <a href="#">新闻资讯</a>
                                        <ul class="sub-menu">
                                            <li><a href="blog.html">公司新闻</a></li>
                                            <li><a href="blog-details.html">行业动态</a></li>
                                        </ul>
                                    </li>
                                    <li><a href="contact.html">联系我们</a></li>
                                </ul>
                            </nav>
                            <div class="navbar-right d-inline-flex d-lg-none">
                                <button type="button" class="menu-toggle sidebar-btn">
                                    <span class="line"></span>
                                    <span class="line"></span>
                                    <span class="line"></span>
                                </button>
                            </div>
                        </div>
                        <div class="col-auto d-none d-lg-block">
                            <div class="header-button">
                                <button type="button" class="simple-icon searchBoxToggler"><i class="far fa-search"></i></button>
                                <button type="button" class="simple-icon sideMenuToggler">
                                    <i class="far fa-shopping-cart"></i>
                                    <span class="badge">5</span>
                                </button>
                                <a href="contact.html" class="th-btn style4">获取报价<i class="fas fa-arrow-right ms-2"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!--==============================
    Mobile Menu
  ==============================-->
    <div class="th-menu-wrapper">
        <div class="th-menu-area text-center">
            <button class="th-menu-toggle"><i class="fal fa-times"></i></button>
            <div class="mobile-logo">
                <a href="home.html"><img src="assets/img/logo.svg" alt="重庆锦雨丰建筑"></a>
            </div>
            <div class="th-mobile-menu">
                <ul>
                    <li><a href="home.html">首页</a></li>
                    <li><a href="about.html">关于我们</a></li>
                    <li class="menu-item-has-children">
                        <a href="#">产品服务</a>
                        <ul class="sub-menu">
                            <li><a href="service.html">玻璃幕墙</a></li>
                            <li><a href="service-details.html">铝合金门窗</a></li>
                            <li><a href="service.html">钢结构工程</a></li>
                            <li><a href="service-details.html">装饰装修</a></li>
                        </ul>
                    </li>
                    <li class="menu-item-has-children">
                        <a href="#">案例展示</a>
                        <ul class="sub-menu">
                            <li><a href="project.html">工程案例</a></li>
                            <li><a href="project-details.html">案例详情</a></li>
                        </ul>
                    </li>
                    <li class="menu-item-has-children">
                        <a href="#">新闻资讯</a>
                        <ul class="sub-menu">
                            <li><a href="blog.html">公司新闻</a></li>
                            <li><a href="blog-details.html">行业动态</a></li>
                        </ul>
                    </li>
                    <li><a href="contact.html">联系我们</a></li>
                </ul>
            </div>
        </div>
    </div>
    <!--==============================
	Hero Area
==============================-->
    <div class="breadcumb-wrapper " data-bg-src="assets/img/bg/breadcumb-bg.jpg" data-overlay="title" data-opacity="8">
        <div class="breadcumb-shape" data-bg-src="assets/img/bg/breadcumb_shape_1_1.png">
        </div>
        <div class="shape-mockup breadcumb-shape2 jump d-lg-block d-none" data-right="30px" data-bottom="30px">
            <img src="assets/img/bg/breadcumb_shape_1_2.png" alt="shape">
        </div>
        <div class="shape-mockup breadcumb-shape3 jump-reverse d-lg-block d-none" data-left="50px" data-bottom="80px">
            <img src="assets/img/bg/breadcumb_shape_1_3.png" alt="shape">
        </div>
        <div class="container">
            <div class="breadcumb-content text-center">
                <h1 class="breadcumb-title">标签云</h1>
                <ul class="breadcumb-menu">
                    <li><a href="home.html">首页</a></li>
                    <li>标签云</li>
                </ul>
            </div>
        </div>
    </div>
    <!--==============================
    Search Box Area
    ==============================-->
    <div class="popup-search-box d-none d-lg-block">
        <button class="searchClose"><i class="fal fa-times"></i></button>
        <form action="search-results.html" method="GET">
            <input type="text" name="q" placeholder="您在寻找什么？">
            <button type="submit"><i class="fal fa-search"></i></button>
        </form>
    </div>

    <!--==============================
						专业标签云区域  
==============================-->
    <section class="th-blog-wrapper space">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <!-- 专业标签云头部 -->
                    <div class="professional-tagcloud-header text-center mb-60">
                        <div class="section-title-wrapper">
                            <span class="sub-title style1">PROFESSIONAL TAG CLOUD</span>
                            <h2 class="sec-title">专业标签云导航系统</h2>
                            <p class="sec-text mb-40">专业的产品和服务分类标签云，通过可视化方式展示我们的业务领域。标签大小反映了内容的丰富程度和专业深度，点击可快速定位到相关产品和服务。</p>
                        </div>
                        
                        <!-- 标签统计信息 -->
                        <div class="tagcloud-stats-container mb-50">
                            <div class="row g-4">
                                <div class="col-lg-3 col-md-6">
                                    <div class="stats-card text-center">
                                        <div class="stats-icon mb-3">
                                            <i class="fas fa-tags"></i>
                                        </div>
                                        <h3 class="stats-number">150+</h3>
                                        <p class="stats-label">专业标签</p>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="stats-card text-center">
                                        <div class="stats-icon mb-3">
                                            <i class="fas fa-layer-group"></i>
                                        </div>
                                        <h3 class="stats-number">8</h3>
                                        <p class="stats-label">主要分类</p>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="stats-card text-center">
                                        <div class="stats-icon mb-3">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <h3 class="stats-number">500+</h3>
                                        <p class="stats-label">项目案例</p>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="stats-card text-center">
                                        <div class="stats-icon mb-3">
                                            <i class="fas fa-award"></i>
                                        </div>
                                        <h3 class="stats-number">15+</h3>
                                        <p class="stats-label">年专业经验</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 核心产品标签云 -->
                    <div class="professional-tagcloud-section mb-60">
                        <div class="section-header mb-40">
                            <div class="row align-items-center">
                                <div class="col-lg-8">
                                    <h3 class="section-title"><i class="fas fa-star text-theme me-3"></i>核心产品系列</h3>
                                    <p class="section-subtitle mb-0">专业建筑幕墙与门窗系统解决方案</p>
                                </div>
                                <div class="col-lg-4 text-lg-end">
                                    <span class="tag-count-badge">主要产品: 4大系列</span>
                                </div>
                            </div>
                        </div>
                        <div class="professional-tagcloud-container">
                            <div class="tagcloud-grid">
                                <a href="search-results.html?tag=玻璃幕墙" class="professional-tag primary-tag" data-count="120+">
                                    <span class="tag-icon"><i class="fas fa-building"></i></span>
                                    <span class="tag-text">玻璃幕墙</span>
                                    <span class="tag-badge">核心业务</span>
                                </a>
                                <a href="search-results.html?tag=铝合金门窗" class="professional-tag primary-tag" data-count="95+">
                                    <span class="tag-icon"><i class="fas fa-window-maximize"></i></span>
                                    <span class="tag-text">铝合金门窗</span>
                                    <span class="tag-badge">主营产品</span>
                                </a>
                                <a href="search-results.html?tag=钢结构工程" class="professional-tag secondary-tag" data-count="68+">
                                    <span class="tag-icon"><i class="fas fa-industry"></i></span>
                                    <span class="tag-text">钢结构工程</span>
                                    <span class="tag-badge">专业工程</span>
                                </a>
                                <a href="search-results.html?tag=装饰装修" class="professional-tag secondary-tag" data-count="45+">
                                    <span class="tag-icon"><i class="fas fa-paint-brush"></i></span>
                                    <span class="tag-text">装饰装修</span>
                                    <span class="tag-badge">配套服务</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 技术特色标签云 -->
                    <div class="professional-tagcloud-section mb-60">
                        <div class="section-header mb-40">
                            <div class="row align-items-center">
                                <div class="col-lg-8">
                                    <h3 class="section-title"><i class="fas fa-cogs text-theme me-3"></i>技术特色与优势</h3>
                                    <p class="section-subtitle mb-0">先进技术与专业工艺的完美结合</p>
                                </div>
                                <div class="col-lg-4 text-lg-end">
                                    <span class="tag-count-badge">技术标签: 12项特色</span>
                                </div>
                            </div>
                        </div>
                        <div class="feature-tagcloud">
                            <a href="search-results.html?tag=节能环保" class="feature-tag level-1">
                                <span class="feature-icon"><i class="fas fa-leaf"></i></span>
                                <span class="feature-name">节能环保</span>
                                <span class="feature-desc">绿色建筑首选</span>
                            </a>
                            <a href="search-results.html?tag=防水密封" class="feature-tag level-1">
                                <span class="feature-icon"><i class="fas fa-shield-alt"></i></span>
                                <span class="feature-name">防水密封</span>
                                <span class="feature-desc">专业防护技术</span>
                            </a>
                            <a href="search-results.html?tag=抗风压" class="feature-tag level-2">
                                <span class="feature-icon"><i class="fas fa-wind"></i></span>
                                <span class="feature-name">抗风压</span>
                                <span class="feature-desc">高层建筑专用</span>
                            </a>
                            <a href="search-results.html?tag=隔音降噪" class="feature-tag level-2">
                                <span class="feature-icon"><i class="fas fa-volume-mute"></i></span>
                                <span class="feature-name">隔音降噪</span>
                                <span class="feature-desc">静音环境营造</span>
                            </a>
                            <a href="search-results.html?tag=保温隔热" class="feature-tag level-2">
                                <span class="feature-icon"><i class="fas fa-thermometer-half"></i></span>
                                <span class="feature-name">保温隔热</span>
                                <span class="feature-desc">节能减排</span>
                            </a>
                            <a href="search-results.html?tag=安全防护" class="feature-tag level-3">
                                <span class="feature-icon"><i class="fas fa-hard-hat"></i></span>
                                <span class="feature-name">安全防护</span>
                                <span class="feature-desc">全方位保障</span>
                            </a>
                            <a href="search-results.html?tag=智能控制" class="feature-tag level-3">
                                <span class="feature-icon"><i class="fas fa-microchip"></i></span>
                                <span class="feature-name">智能控制</span>
                                <span class="feature-desc">科技创新</span>
                            </a>
                            <a href="search-results.html?tag=LOW-E玻璃" class="feature-tag level-3">
                                <span class="feature-icon"><i class="fas fa-gem"></i></span>
                                <span class="feature-name">LOW-E玻璃</span>
                                <span class="feature-desc">高端配置</span>
                            </a>
                        </div>
                    </div>

                    <!-- 细分产品标签 -->
                    <div class="professional-tagcloud-section mb-60">
                        <div class="section-header mb-40">
                            <div class="row align-items-center">
                                <div class="col-lg-8">
                                    <h3 class="section-title"><i class="fas fa-th-large text-theme me-3"></i>细分产品分类</h3>
                                    <p class="section-subtitle mb-0">丰富的产品线满足不同项目需求</p>
                                </div>
                                <div class="col-lg-4 text-lg-end">
                                    <span class="tag-count-badge">产品类型: 20+种</span>
                                </div>
                            </div>
                        </div>
                        <div class="detailed-tagcloud">
                            <div class="tag-category-group">
                                <h4 class="category-title"><i class="fas fa-glass-whiskey me-2"></i>玻璃产品</h4>
                                <div class="category-tags">
                                    <a href="search-results.html?tag=节能玻璃" class="detail-tag">节能玻璃</a>
                                    <a href="search-results.html?tag=防火玻璃" class="detail-tag">防火玻璃</a>
                                    <a href="search-results.html?tag=中空玻璃" class="detail-tag">中空玻璃</a>
                                    <a href="search-results.html?tag=夹胶玻璃" class="detail-tag">夹胶玻璃</a>
                                    <a href="search-results.html?tag=钢化玻璃" class="detail-tag">钢化玻璃</a>
                                </div>
                            </div>
                            <div class="tag-category-group">
                                <h4 class="category-title"><i class="fas fa-door-open me-2"></i>门窗系列</h4>
                                <div class="category-tags">
                                    <a href="search-results.html?tag=系统门窗" class="detail-tag">系统门窗</a>
                                    <a href="search-results.html?tag=推拉门" class="detail-tag">推拉门</a>
                                    <a href="search-results.html?tag=平开窗" class="detail-tag">平开窗</a>
                                    <a href="search-results.html?tag=百叶窗" class="detail-tag">百叶窗</a>
                                    <a href="search-results.html?tag=隔热断桥" class="detail-tag">隔热断桥</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 应用场景标签云 -->
                    <div class="professional-tagcloud-section mb-60">
                        <div class="section-header mb-40">
                            <div class="row align-items-center">
                                <div class="col-lg-8">
                                    <h3 class="section-title"><i class="fas fa-map-marker-alt text-theme me-3"></i>应用场景分布</h3>
                                    <p class="section-subtitle mb-0">广泛应用于各类建筑项目</p>
                                </div>
                                <div class="col-lg-4 text-lg-end">
                                    <span class="tag-count-badge">应用领域: 10+个</span>
                                </div>
                            </div>
                        </div>
                        <div class="application-tagcloud">
                            <div class="row g-3">
                                <div class="col-lg-3 col-md-4 col-sm-6">
                                    <a href="search-results.html?tag=商业建筑" class="application-card">
                                        <div class="app-icon"><i class="fas fa-store"></i></div>
                                        <h5 class="app-title">商业建筑</h5>
                                        <p class="app-count">180+ 项目</p>
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-4 col-sm-6">
                                    <a href="search-results.html?tag=住宅建筑" class="application-card">
                                        <div class="app-icon"><i class="fas fa-home"></i></div>
                                        <h5 class="app-title">住宅建筑</h5>
                                        <p class="app-count">220+ 项目</p>
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-4 col-sm-6">
                                    <a href="search-results.html?tag=办公楼" class="application-card">
                                        <div class="app-icon"><i class="fas fa-building"></i></div>
                                        <h5 class="app-title">办公楼</h5>
                                        <p class="app-count">95+ 项目</p>
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-4 col-sm-6">
                                    <a href="search-results.html?tag=酒店" class="application-card">
                                        <div class="app-icon"><i class="fas fa-bed"></i></div>
                                        <h5 class="app-title">酒店建筑</h5>
                                        <p class="app-count">45+ 项目</p>
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-4 col-sm-6">
                                    <a href="search-results.html?tag=医院" class="application-card">
                                        <div class="app-icon"><i class="fas fa-hospital"></i></div>
                                        <h5 class="app-title">医疗建筑</h5>
                                        <p class="app-count">32+ 项目</p>
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-4 col-sm-6">
                                    <a href="search-results.html?tag=学校" class="application-card">
                                        <div class="app-icon"><i class="fas fa-graduation-cap"></i></div>
                                        <h5 class="app-title">教育建筑</h5>
                                        <p class="app-count">28+ 项目</p>
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-4 col-sm-6">
                                    <a href="search-results.html?tag=购物中心" class="application-card">
                                        <div class="app-icon"><i class="fas fa-shopping-cart"></i></div>
                                        <h5 class="app-title">购物中心</h5>
                                        <p class="app-count">18+ 项目</p>
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-4 col-sm-6">
                                    <a href="search-results.html?tag=工业建筑" class="application-card">
                                        <div class="app-icon"><i class="fas fa-industry"></i></div>
                                        <h5 class="app-title">工业建筑</h5>
                                        <p class="app-count">25+ 项目</p>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 专业服务说明 -->
                    <div class="professional-service-note">
                        <div class="row align-items-center">
                            <div class="col-lg-8">
                                <div class="service-note-content">
                                    <h4 class="note-title">专业建筑幕墙与门窗系统解决方案提供商</h4>
                                    <p class="note-text">重庆锦雨丰建筑有限公司拥有15年专业经验，为各类建筑项目提供从设计、生产到安装的一体化服务。我们的产品广泛应用于商业、住宅、办公、酒店、医院等各类建筑项目。</p>
                                </div>
                            </div>
                            <div class="col-lg-4 text-lg-end">
                                <a href="contact.html" class="th-btn style2">联系专业顾问<i class="fas fa-arrow-right ms-2"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!--==============================
			Footer Area
==============================-->
    <footer class="footer-wrapper footer-layout1" data-bg-src="assets/img/bg/footer_bg_1.png">
        <div class="widget-area">
            <div class="container">
                <div class="row justify-content-between">
                    <div class="col-md-6 col-xxl-3 col-xl-4">
                        <div class="widget footer-widget">
                            <div class="th-widget-about">
                                <div class="about-logo">
                                    <a href="home.html"><img src="assets/img/logo-white.svg" alt="重庆锦雨丰建筑"></a>
                                </div>
                                <p class="about-text">重庆锦雨丰建筑有限公司专注于玻璃幕墙、铝合金门窗等建筑装饰领域，为客户提供专业、优质的服务。</p>
                                <div class="th-social">
                                    <a href="https://www.facebook.com/"><i class="fab fa-facebook-f"></i></a>
                                    <a href="https://www.twitter.com/"><i class="fab fa-twitter"></i></a>
                                    <a href="https://www.linkedin.com/"><i class="fab fa-linkedin-in"></i></a>
                                    <a href="https://www.instagram.com/"><i class="fab fa-instagram"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-xl-auto">
                        <div class="widget widget_nav_menu footer-widget">
                            <h3 class="widget_title">快速链接</h3>
                            <div class="menu-all-pages-container">
                                <ul class="menu">
                                    <li><a href="about.html">关于我们</a></li>
                                    <li><a href="service.html">产品服务</a></li>
                                    <li><a href="project.html">工程案例</a></li>
                                    <li><a href="blog.html">新闻资讯</a></li>
                                    <li><a href="contact.html">联系我们</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-xl-auto">
                        <div class="widget widget_nav_menu footer-widget">
                            <h3 class="widget_title">产品分类</h3>
                            <div class="menu-all-pages-container">
                                <ul class="menu">
                                    <li><a href="service.html">玻璃幕墙</a></li>
                                    <li><a href="service.html">铝合金门窗</a></li>
                                    <li><a href="service.html">钢结构工程</a></li>
                                    <li><a href="service.html">装饰装修</a></li>
                                    <li><a href="service.html">特种玻璃</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-xxl-3 col-xl-4">
                        <div class="widget footer-widget">
                            <h3 class="widget_title">联系信息</h3>
                            <div class="th-widget-contact">
                                <div class="info-box_text">
                                    <div class="icon">
                                        <img src="assets/img/icon/phone.svg" alt="电话">
                                    </div>
                                    <div class="details">
                                        <p><a href="tel:+02340081222" class="info-box_link">023-4008-1222</a></p>
                                    </div>
                                </div>
                                <div class="info-box_text">
                                    <div class="icon">
                                        <img src="assets/img/icon/envelope.svg" alt="邮箱">
                                    </div>
                                    <div class="details">
                                        <p><a href="mailto:<EMAIL>" class="info-box_link"><EMAIL></a></p>
                                    </div>
                                </div>
                                <div class="info-box_text">
                                    <div class="icon">
                                        <img src="assets/img/icon/location-dot.svg" alt="地址">
                                    </div>
                                    <div class="details">
                                        <p>重庆市江北区观音桥街道</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="copyright-wrap">
            <div class="container">
                <div class="row justify-content-between align-items-center">
                    <div class="col-md-6">
                        <p class="copyright-text">Copyright <i class="fal fa-copyright"></i> 2024 <a href="home.html">重庆锦雨丰建筑有限公司</a>. 版权所有</p>
                    </div>
                    <div class="col-md-6 text-end d-none d-md-block">
                        <div class="footer-links">
                            <a href="#">隐私政策</a>
                            <a href="#">服务条款</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!--********************************
			Code End  Here 
	********************************-->

    <!-- Scroll To Top -->
    <div class="scroll-top">
        <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
            <path d="m50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" style="transition: stroke-dashoffset 10ms linear 0s; stroke-dasharray: 307.919, 307.919; stroke-dashoffset: 307.919;"></path>
        </svg>
    </div>

    <!--==============================
    All Js File
============================== -->
    <!-- Jquery -->
    <script src="assets/js/vendor/jquery-3.6.0.min.js"></script>
    <!-- Swiper Js -->
    <script src="assets/js/swiper-bundle.min.js"></script>
    <!-- Bootstrap -->
    <script src="assets/js/bootstrap.min.js"></script>
    <!-- Magnific Popup -->
    <script src="assets/js/jquery.magnific-popup.min.js"></script>
    <!-- Counter Up -->
    <script src="assets/js/jquery.counterup.min.js"></script>
    <!-- Tilt -->
    <script src="assets/js/tilt.jquery.min.js"></script>
    <!-- Isotope Filter -->
    <script src="assets/js/imagesloaded.pkgd.min.js"></script>
    <script src="assets/js/isotope.pkgd.min.js"></script>

    <!-- Main Js File -->
    <script src="assets/js/main.js"></script>


</body>
</html>