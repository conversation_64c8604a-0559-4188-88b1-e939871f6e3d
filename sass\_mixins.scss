@import "variables";

//---------- @mixins ----------//

@mixin box-shadow($val...) {
	box-shadow: ($val);
}

@mixin border-radius($val) {
	border-radius: ($val);
}

@mixin translateX($val...) {
	transform: translateX($val);
}

@mixin translateY($val...) {
	transform: translateY($val);
}

@mixin translate3d($val, $val2, $val3) {
	transform: translate3d($val, $val2, $val3);
}

@mixin rotate($val) {
	transform: rotate($val);
}

@mixin scale($val) {
	transform: scale($val);
}

@function set-color($color) {
	@if (lightness($color) > 50) {
		@return $black;
	} @else {
		@return $white;
	}
}

@mixin grid-size($grid-columns, $grid-size-child) {
	@for $grid-img from 1 through $grid-columns {
		&.grid-#{$grid-img} #{$grid-size-child} {
			width: divide(100%, $grid-img);
		}
	}
}

@mixin placeholder {
	&::-webkit-input-placeholder {
		@content;
	}
	&::-moz-placeholder {
		@content;
	}
	&:-ms-input-placeholder {
		@content;
	}
}

@mixin align-position($args) {
	$offsets: top right bottom left;

	@each $o in $offsets {
		$i: index($args, $o);

		@if $i and
			$i +
			1 <=
			length($args) and
			type-of(nth($args, $i + 1)) ==
			number
		{
			#{$o}: nth($args, $i + 1);
		}
	}
}


@mixin font-weight($font-weight) {
	@for $i from 1 through $font-weight {
		@if $i % 100 == 0 {
			.t#{$i} {
				font-weight: $i !important;
			}
		}
	}
}

// Div Center
@mixin center($horizontal: true, $vertical: true) {
	position: absolute;

	@if ($horizontal and $vertical) {
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	} @else if ($horizontal) {
		left: 50%;
		transform: translate(-50%, 0);
	} @else if ($vertical) {
		top: 50%;
		transform: translate(0, -50%);
	}
}

@mixin keyframes($animationName) {
	@-webkit-keyframes #{$animationName} {
		@content;
	}
	@keyframes #{$animationName} {
		@content;
	}
}
@mixin animate($name...) {
	-webkit-animation: $name 30s infinite linear;
	animation: $name 30s infinite linear;
}

@mixin variant($selector) {
	@at-root #{$selector}#{&} {
		@content;
	}
}

@function decimal-round($number, $digits: 0, $mode: round) {
	$n: 1;
	// $number must be a number
	@if type-of($number) != number {
		@warn '#{ $number } is not a number.';
		@return $number;
	}
	// $digits must be a unitless number
	@if type-of($digits) != number {
		@warn '#{ $digits } is not a number.';
		@return $number;
	} @else if not unitless($digits) {
		@warn '#{ $digits } has a unit.';
		@return $number;
	}
	@for $i from 1 through $digits {
		$n: $n * 10;
	}
	@if $mode == round {
		@return divide(round($number * $n), $n);
	} @else if $mode == ceil {
		@return divide(ceil($number * $n), $n);
	} @else if $mode == floor {
		@return divide(floor($number * $n), $n);
	} @else {
		@warn '#{ $mode } is undefined keyword.';
		@return $number;
	}
}
@function decimal-ceil($number, $digits: 0) {
	@return decimal-round($number, $digits, ceil);
}
@function decimal-floor($number, $digits: 0) {
	@return decimal-round($number, $digits, floor);
}

@mixin opacity($opacity, $important: 0) {
	@if $important == 1 {
		opacity: $opacity !important;
		$opacity-ie: $opacity * 100;
		filter: alpha(opacity=$opacity-ie) !important; //IE8
	} @else {
		opacity: $opacity;
		$opacity-ie: $opacity * 100;
		filter: alpha(opacity=$opacity-ie); //IE8
	}
}

@function set-border-color($color) {
	@if (lightness($color) > 50) {
		@return $line-color;
	} @else {
		@return rgba(white, 0.15);
	}
}