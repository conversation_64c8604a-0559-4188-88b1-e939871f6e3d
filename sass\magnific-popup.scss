/* Magnific Popup SCSS */

////////////////////////
//      Settings      //
////////////////////////

$mfp-include-arrows: true !default; // Include styles for nav arrows
$mfp-include-iframe-type: true !default; // Enable Iframe-type popups
$mfp-include-image-type: true !default; // Enable Image-type popups
$mfp-include-mobile-layout-for-image: true !default; // Removes paddings from top and bottom
$mfp-use-visuallyhidden: false !default; // Hide content from browsers, but make it available for screen readers

:root {
    // overlay
    --#{$cnvs-prefix}mfp-overlay-color: #0b0b0b; // Color of overlay screen
    --#{$cnvs-prefix}mfp-overlay-opacity: 0.9; // Opacity of overlay screen
    --#{$cnvs-prefix}mfp-shadow: 0 0 8px rgba(0, 0, 0, 0.6); // Shadow on image or iframe

    // spacing
    --#{$cnvs-prefix}mfp-popup-padding-left: 8px; // Padding from left and from right side
    --#{$cnvs-prefix}mfp-popup-padding-left-mobile: 6px; // Same as above, but is applied when width of window is less than 800px

    --#{$cnvs-prefix}mfp-z-index-base: 1040; // Base z-index of popup

    // controls
    --#{$cnvs-prefix}mfp-controls-opacity: 0.65; // Opacity of controls
    --#{$cnvs-prefix}mfp-controls-color: #FFF; // Color of controls
    --#{$cnvs-prefix}mfp-inner-close-icon-color: #333; // Color of close button when inside
    --#{$cnvs-prefix}mfp-controls-text-color: #CCC; // Color of preloader and "1 of X" indicator
    --#{$cnvs-prefix}mfp-controls-text-color-hover: #FFF; // Hover color of preloader and "1 of X" indicator
    --#{$cnvs-prefix}mfp-preloader-size: 2rem;
    --#{$cnvs-prefix}mfp-close-btn-size: 4rem;
    --#{$cnvs-prefix}mfp-close-btn-font-size: 1.5rem;
    --#{$cnvs-prefix}mfp-arrow-size: 4rem;
    --#{$cnvs-prefix}mfp-title-padding: 1rem;
    --#{$cnvs-prefix}mfp-title-border-radius: 0.25rem;
    --#{$cnvs-prefix}mfp-link-color: #CCC;

    // Iframe-type options
    --#{$cnvs-prefix}mfp-iframe-padding-top: 40px; // Iframe padding top
    --#{$cnvs-prefix}mfp-iframe-background: #000; // Background color of iframes
    --#{$cnvs-prefix}mfp-iframe-max-width: 900px; // Maximum width of iframes
    --#{$cnvs-prefix}mfp-iframe-ratio: 9/16; // Ratio of iframe (9/16 = widescreen, 3/4 = standard, etc.)

    // Image-type options
    --#{$cnvs-prefix}mfp-image-background: #444;

    // Image caption options
    --#{$cnvs-prefix}mfp-caption-title-color: #F3F3F3; // Caption title color
    --#{$cnvs-prefix}mfp-caption-subtitle-color: #BDBDBD; // Caption subtitle color
}

////////////////////////
//
// Contents:
//
// 1. General styles
//    - Transluscent overlay
//    - Containers, wrappers
//    - Cursors
//    - Helper classes
// 2. Appearance
//    - Preloader & text that displays error messages
//    - CSS reset for buttons
//    - Close icon
//    - "1 of X" counter
//    - Navigation (left/right) arrows
//    - Iframe content type styles
//    - Image content type styles
//    - Media query where size of arrows is reduced
//    - IE7 support
//
////////////////////////



////////////////////////
// 1. General styles
////////////////////////

// Transluscent overlay
.mfp-bg {
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: calc(var(--#{$cnvs-prefix}mfp-z-index-base) + 2);
	overflow: hidden;
	position: fixed;

	background: var(--#{$cnvs-prefix}mfp-overlay-color);
	opacity: var(--#{$cnvs-prefix}mfp-overlay-opacity);
}

// Wrapper for popup
.mfp-wrap {
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: calc(var(--#{$cnvs-prefix}mfp-z-index-base) + 3);
	position: fixed;
	outline: none !important;
	-webkit-backface-visibility: hidden; // fixes webkit bug that can cause "false" scrollbar
}

// Root container
.mfp-container {
	text-align: center;
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	padding: 0 var(--#{$cnvs-prefix}mfp-popup-padding-left);
	box-sizing: border-box;
}

// Vertical centerer helper
.mfp-container {
	&::before {
		content: '';
		display: inline-block;
		height: 100%;
		vertical-align: middle;
	}
}

// Remove vertical centering when popup has class `mfp-align-top`
.mfp-align-top {
	.mfp-container {
		&::before {
			display: none;
		}
	}
}

// Popup content holder
.mfp-content {
	position: relative;
	display: inline-block;
	vertical-align: middle;
	margin: 0 auto;
	text-align: left;
	z-index: calc(var(--#{$cnvs-prefix}mfp-z-index-base) + 5);
}
.mfp-inline-holder,
.mfp-ajax-holder {
	.mfp-content {
		width: 100%;
		cursor: auto;
	}
}

// Cursors
.mfp-ajax-cur {
	cursor: progress;
}
.mfp-zoom-out-cur {
	&, .mfp-image-holder .mfp-close {
		cursor: -moz-zoom-out;
		cursor: -webkit-zoom-out;
		cursor: zoom-out;
	}
}
.mfp-zoom {
	cursor: pointer;
	cursor: -webkit-zoom-in;
	cursor: -moz-zoom-in;
	cursor: zoom-in;
}
.mfp-auto-cursor {
	.mfp-content {
		cursor: auto;
	}
}

.mfp-close,
.mfp-arrow,
.mfp-preloader,
.mfp-counter {
	-webkit-user-select:none;
	-moz-user-select: none;
	user-select: none;
}

// Hide the image during the loading
.mfp-loading {
	&.mfp-figure {
		display: none;
	}
}

// Helper class that hides stuff
@if $mfp-use-visuallyhidden {
	// From HTML5 Boilerplate https://github.com/h5bp/html5-boilerplate/blob/v4.2.0/doc/css.md#visuallyhidden
	.mfp-hide {
		border: 0 !important;
		clip: rect(0 0 0 0) !important;
		height: 1px !important;
		margin: -1px !important;
		overflow: hidden !important;
		padding: 0 !important;
		position: absolute !important;
		width: 1px !important;
	}
} @else {
	.mfp-hide {
		display: none !important;
	}
}


////////////////////////
// 2. Appearance
////////////////////////

// Preloader and text that displays error messages
.mfp-preloader {
	color: var(--#{$cnvs-prefix}mfp-controls-text-color);
	z-index: calc(var(--#{$cnvs-prefix}mfp-z-index-base) + 4);
    position: absolute;
    top: 50%;
    left: 50%;
    width: var(--#{$cnvs-prefix}mfp-preloader-size);
    height: var(--#{$cnvs-prefix}mfp-preloader-size);
    font-size: var(--#{$cnvs-prefix}mfp-preloader-size);
    line-height: 1;
    text-align: center;
    transform: translate(-50%, -50%);
	font-family: "bootstrap-icons";
	line-height: inherit;
	a {
		color: var(--#{$cnvs-prefix}mfp-controls-text-color);
		&:hover {
			color: var(--#{$cnvs-prefix}mfp-controls-text-color-hover);
		}
	}
}

.mfp-container:not(.mfp-s-error) .mfp-preloader::before {
	display: block;
    width: 100%;
    height: 100%;
    line-height: 1;
    content: "\F130";
	animation: spin 2s infinite linear;
}

.mfp-container.mfp-s-error .mfp-preloader {
    width: auto;
    height: auto;
    font-size: 1.5rem;
    font-family: inherit;
    animation: none;
    line-height: 1.5;
}

// Hide preloader when content successfully loaded
.mfp-s-ready {
	.mfp-preloader {
		display: none;
	}
}

// Hide content when it was not loaded
.mfp-s-error {
	.mfp-content {
		display: none;
	}
}

// CSS-reset for buttons
button {
	&.mfp-close,
	&.mfp-arrow {
		overflow: visible;
		cursor: pointer;
		background: transparent;
		border: 0;
		-webkit-appearance: none;
		display: block;
		outline: none;
		padding: 0;
		z-index: calc(var(--#{$cnvs-prefix}mfp-z-index-base) + 6);
		box-shadow: none;
		touch-action: manipulation;
	}
	&::-moz-focus-inner {
			padding: 0;
			border: 0
	}
}


// Close icon
.mfp-close {
    width: var(--#{$cnvs-prefix}mfp-close-btn-size);
    height: var(--#{$cnvs-prefix}mfp-close-btn-size);
    line-height: var(--#{$cnvs-prefix}mfp-close-btn-size);

	position: absolute;
	right: 0;
	top: 0;
	text-decoration: none;
	text-align: center;
	opacity: var(--#{$cnvs-prefix}mfp-controls-opacity);
	padding: 0 0 18px 10px;
	color: var(--#{$cnvs-prefix}mfp-controls-color);

	font-style: normal;
	font-size: var(--#{$cnvs-prefix}mfp-close-btn-font-size);

	&:hover,
	&:focus {
		opacity: 1;
	}

	&:active {
		top: 1px;
	}
}
.mfp-close-btn-in {
	.mfp-close {
		color: var(--#{$cnvs-prefix}mfp-inner-close-icon-color);
	}
}
.mfp-image-holder,
.mfp-iframe-holder {
	.mfp-close {
		color: var(--#{$cnvs-prefix}mfp-controls-color);
		right: -6px;
		text-align: right;
		padding-right: 6px;
		width: 100%;
	}
}

// "1 of X" counter
.mfp-counter {
	position: absolute;
	top: auto;
	bottom: 25px;
	right: 20px;
	font-size: 0.75rem;
	line-height: 18px;
	white-space: nowrap;
	padding: 0.5rem 1rem;
	background: rgba(0, 0, 0, 0.5);
	border-radius:  0.25rem;
	color: var(--#{$cnvs-prefix}mfp-controls-text-color);
    &:empty {
        display: none;
    }
}

// Navigation arrows
@if $mfp-include-arrows {
	.mfp-arrow {
		opacity: var(--#{$cnvs-prefix}mfp-controls-opacity);
		position: absolute;
        margin: 0;
        top: 50%;
        left: 0;
        padding: 0;
        width: var(--#{$cnvs-prefix}mfp-arrow-size);
        height: var(--#{$cnvs-prefix}mfp-arrow-size);
        font-family: "bootstrap-icons";
		line-height: inherit;
        -webkit-tap-highlight-color: transparent;
        font-size: calc(var(--#{$cnvs-prefix}mfp-arrow-size) / 2);
        transform: translateY(-50%);
        color: var(--#{$cnvs-prefix}mfp-link-color);
        transition: all .3s ease;
		&:hover {
			opacity: 1;
            transform: translateY(-50%) scale(1.2);
		}
	}

    @media (max-width: 991.98px) {
        .mfp-arrow {
            background-color: rgba(0, 0, 0, 0.75) !important;
        }

        .mfp-arrow:hover {
            transform: translateY(-50%);
            background-color: rgba(0, 0, 0, 0.85) !important;
        }
    }

	.mfp-arrow-left::before,
	.mfp-arrow-right::before {
		display: inline-flex;
		align-self: center;
		justify-self: center;
	}

    .mfp-arrow-right::before {
        content: "\F285";
    }

    .mfp-arrow-right {
        left: auto;
        right: 0;
    }

    .mfp-arrow-left {
        right: auto;
        left: 0;
    }

    .mfp-arrow-left::before {
        content: "\F284";
    }
}



// Iframe content type
@if $mfp-include-iframe-type {
	.mfp-iframe-holder {
		padding-top: var(--#{$cnvs-prefix}mfp-iframe-padding-top);
		padding-bottom: var(--#{$cnvs-prefix}mfp-iframe-padding-top);
		.mfp-content {
			line-height: 0;
			width: 100%;
			max-width: var(--#{$cnvs-prefix}mfp-iframe-max-width);
		}
		.mfp-close {
			top: -40px;
		}
	}
	.mfp-iframe-scaler {
		width: 100%;
		height: 0;
		overflow: hidden;
		padding-top: calc(var(--#{$cnvs-prefix}mfp-iframe-ratio) * 100%);
		iframe {
			position: absolute;
			display: block;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			box-shadow: var(--#{$cnvs-prefix}mfp-shadow);
			background: var(--#{$cnvs-prefix}mfp-iframe-background);
		}
	}
}



// Image content type
@if $mfp-include-image-type {

	/* Main image in popup */
	img {
		&.mfp-img {
			width: auto;
			max-width: 100%;
			height: auto;
			display: block;
			line-height: 0;
			box-sizing: border-box;
			margin: 0 auto;
		}
	}

	/* The shadow behind the image */
	.mfp-figure {
		line-height: 0;
		&::after {
			content: '';
			position: absolute;
			left: 0;
			top: 0;
			bottom: 0;
			display: block;
			right: 0;
			width: auto;
			height: auto;
			z-index: -1;
			box-shadow: var(--#{$cnvs-prefix}mfp-shadow);
			background: var(--#{$cnvs-prefix}mfp-image-background);
		}
		small {
			color: var(--#{$cnvs-prefix}mfp-caption-subtitle-color);
			display: block;
			font-size: 12px;
			line-height: 14px;
            &:empty {
                display: none;
            }
		}
		figure {
			margin: 0;
		}
	}
	.mfp-bottom-bar {
		margin-top: calc(-40px + 4px);
		position: absolute;
		top: 100%;
		left: 0;
		width: 100%;
		cursor: auto;
	}
	.mfp-title {
		position: absolute;
        left: 50%;
        bottom: 25px;
        transform: translateX(-50%);
        width: auto;
        text-align: center;
        line-height: 1.65;
        color: var(--#{$cnvs-prefix}mfp-caption-title-color);
        word-wrap: break-word;
        padding: var(--#{$cnvs-prefix}mfp-title-padding);
        background: rgba(39, 60, 68, .2);
        -webkit-backdrop-filter: blur(3em);
        backdrop-filter: blur(3em);
        border-radius: var(--#{$cnvs-prefix}mfp-title-border-radius);
        &:empty {
            display: none;
        }
	}

	.mfp-image-holder {
		.mfp-content {
			max-width: 100%;
		}
	}

	.mfp-gallery {
		.mfp-image-holder {
			.mfp-figure {
				cursor: pointer;
			}
		}
	}


	@if $mfp-include-mobile-layout-for-image {
		@media screen and (max-width: 800px) and (orientation:landscape), screen and (max-height: 300px) {
			/**
			 * Remove all paddings around the image on small screen
			 */
			.mfp-img-mobile {
				.mfp-image-holder {
					padding-left: 0;
					padding-right: 0;
				}
				img {
					&.mfp-img {
						padding: 0;
					}
				}
				.mfp-figure {
					// The shadow behind the image
					&::after {
						top: 0;
						bottom: 0;
					}
					small {
						display: inline;
						margin-left: 5px;
					}
				}
				.mfp-bottom-bar {
					background: rgba(0,0,0,0.6);
					bottom: 0;
					margin: 0;
					top: auto;
					padding: 3px 5px;
					position: fixed;
					box-sizing: border-box;
					&:empty {
						padding: 0;
					}
				}
				.mfp-counter {
					right: 5px;
					top: 3px;
				}
				.mfp-close {
					top: 0;
					right: 0;
					width: 35px;
					height: 35px;
					line-height: 35px;
					background: rgba(0, 0, 0, 0.6);
					position: fixed;
					text-align: center;
					padding: 0;
				}
			}
		}
	}
}

// Scale navigation arrows and reduce padding from sides
@media all and (max-width: 900px) {
	.mfp-arrow {
		-webkit-transform: translateY(-50%) scale(0.75) !important;
		transform: translateY(-50%) scale(0.75) !important;
	}
	.mfp-arrow-left {
		-webkit-transform-origin: 0;
		transform-origin: 0;
	}
	.mfp-arrow-right {
		-webkit-transform-origin: 100%;
		transform-origin: 100%;
	}
	.mfp-container {
		padding-left: var(--#{$cnvs-prefix}mfp-popup-padding-left-mobile);
		padding-right: var(--#{$cnvs-prefix}mfp-popup-padding-left-mobile);
	}
}
