/*----------------------------------------------------------------
	Canvas: Beauty
-----------------------------------------------------------------*/

:root {
	--cnvs-themecolor: #6A1B9A;
	--cnvs-themecolor-rgb: 106, 27, 154;
	--cnvs-color1: #5a257f;
	--cnvs-color2: #8cdc8c;
	--cnvs-color3: #dd5980;
	--cnvs-color4: #d5a651;
	--cnvs-primary-font: 'Merriweather', serif;
	--cnvs-body-font: 'Lato', san-serif;
}

/* CSS Over 992px Devices */
@media (min-width: 992px) {

	.side-header .menu-link {
		font-size: 0.875rem;
		text-transform: none;
		font-weight: 400;
		letter-spacing: 1px;
		color: #111;
		-webkit-transition: transform .3s ease;
		-o-transition: transform .3s ease;
		transition: transform .3s ease;
	}

	.side-header .primary-menu .menu-link:hover {
		-webkit-transform: translateX(3px);
		-ms-transform: translateX(3px);
		-o-transform: translateX(3px);
		transform: translateX(3px);
	}
}

.bg-overlay-content h3 {
	color: #FFF;
	font-size: 17px;
	font-weight: 400;
	text-transform: none;
}

thead tr th {
	width: 80%;
	border-top: 0 !important;
	font-family: var(--cnvs-primary-font);
}

.table {
	--bs-table-bg: transparent;
}

.table > :not(caption) > * > * {
	border-bottom-color: rgba(var(--cnvs-contrast-rgb), .1);
	box-shadow: none;
}

.table th,
.table td {
	padding: 0.88rem;
	font-family: var(--cnvs-primary-font);
	font-weight: 400;
	font-size: 15px;
}

.media-box .fbox-media { margin-bottom: 16px; }

.media-box .fbox-media  a { font-size: 38px; }

.media-box.color1 .fbox-media a { color: var(--cnvs-color1); }
.media-box.color2 .fbox-media a { color: var(--cnvs-color2); }
.media-box.color3 .fbox-media a { color: var(--cnvs-color3); }
.media-box.color4 .fbox-media a { color: var(--cnvs-color4); }


/* ----------------------------------------------------------------
	Countdown
-----------------------------------------------------------------*/

.countdown-row {
	text-align: left;
}

.countdown-section {
	font-size: 13px;
	line-height: 1;
	text-align: left;
	border: 0;
	color: #888;
	width: 25%;
	letter-spacing: 1px;
	text-transform: uppercase;
}

.countdown-section:first-child { padding-left: 0; }

.countdown-amount {
	font-size: 38px;
	font-weight: 700;
	font-family: var(--cnvs-primary-font);
	color: #111;
	margin-bottom: 14px;
}

.countdown-period { letter-spacing: 2px; }

.footer-big-contacts {
	color: #222;
	font-size: 16px;
	font-weight: 700;
	font-family: var(--cnvs-primary-font);
	color: #000;
}

.footer-big-contacts span {
	display: block;
	font-size: 11px;
	font-weight: 400;
	color: #888;
	letter-spacing: 0;
}

.mfp-close-btn-in .mfp-close { color: #FFF !important }

.dark.line { border-color: rgba(0,0,0,0.08); }

/* .twentytwenty-horizontal */
.twentytwenty-horizontal .twentytwenty-handle::before,
.twentytwenty-horizontal .twentytwenty-handle::after {
	content: " ";
	display: block;
	background: white;
	position: absolute;
	z-index: 30;
	-webkit-box-shadow: 0px 0px 12px rgba(51, 51, 51, 0.5);
	-moz-box-shadow: 0px 0px 12px rgba(51, 51, 51, 0.5);
	box-shadow: 0px 0px 12px rgba(51, 51, 51, 0.5);
}

.twentytwenty-horizontal .twentytwenty-handle::before,
.twentytwenty-horizontal .twentytwenty-handle::after {
	width: 3px;
	height: 9999px;
	left: 50%;
	margin-left: -1.5px;
}

.twentytwenty-before-label,
.twentytwenty-after-label,
.twentytwenty-overlay {
	position: absolute;
	top: 0;
	width: 100%;
	height: 100%;
}

.twentytwenty-before-label,
.twentytwenty-after-label,
.twentytwenty-overlay {
	-webkit-transition-duration: 0.5s;
	-moz-transition-duration: 0.5s;
	transition-duration: 0.5s;
}

.twentytwenty-before-label,
.twentytwenty-after-label {
	-webkit-transition-property: opacity;
	-moz-transition-property: opacity;
	transition-property: opacity;
}

.twentytwenty-before-label::before,
.twentytwenty-after-label::before {
	color: white;
	font-size: 13px;
	letter-spacing: 0.1em;
}

.twentytwenty-before-label::before,
.twentytwenty-after-label::before {
	position: absolute;
	background: rgba(255, 255, 255, 0.2);
	line-height: 38px;
	padding: 0 20px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
}

.twentytwenty-horizontal .twentytwenty-before-label::before,
.twentytwenty-horizontal .twentytwenty-after-label::before {
	top: 50%;
	margin-top: -19px;
}

.twentytwenty-left-arrow,
.twentytwenty-right-arrow {
	width: 0;
	height: 0;
	border: 6px inset transparent;
	position: absolute;
}

.twentytwenty-left-arrow,
.twentytwenty-right-arrow {
	top: 50%;
	margin-top: -6px;
}

.twentytwenty-container {
	-webkit-box-sizing: content-box;
	-moz-box-sizing: content-box;
	box-sizing: content-box;
	z-index: 0;
	overflow: hidden;
	position: relative;
	-webkit-user-select: none;
	-moz-user-select: none;
}
.twentytwenty-container img {
	width: 100%;
	position: absolute;
	top: 0;
	display: block;
}
.twentytwenty-container.active .twentytwenty-overlay,
.twentytwenty-container.active:hover.twentytwenty-overlay { background: rgba(0, 0, 0, 0); }

.twentytwenty-container.active .twentytwenty-overlay .twentytwenty-before-label,
.twentytwenty-container.active .twentytwenty-overlay .twentytwenty-after-label,
.twentytwenty-container.active:hover.twentytwenty-overlay .twentytwenty-before-label,
.twentytwenty-container.active:hover.twentytwenty-overlay .twentytwenty-after-label { opacity: 0; }

.twentytwenty-container * {
	-webkit-box-sizing: content-box;
	-moz-box-sizing: content-box;
	box-sizing: content-box;
}

.twentytwenty-before-label { opacity: 0; }
.twentytwenty-before-label::before { content: "Before"; }

.twentytwenty-after-label { opacity: 0; }
.twentytwenty-after-label::before { content: "After"; }

.twentytwenty-horizontal .twentytwenty-before-label::before { left: 10px; }
.twentytwenty-horizontal .twentytwenty-after-label::before { right: 10px; }

.twentytwenty-overlay {
	-webkit-transition-property: background;
	-moz-transition-property: background;
	transition-property: background;
	background: rgba(0, 0, 0, 0);
	z-index: 25;
}

.twentytwenty-overlay:hover .twentytwenty-after-label { opacity: 1; }
.twentytwenty-overlay:hover .twentytwenty-before-label { opacity: 1; }

.twentytwenty-before { z-index: 20; }
.twentytwenty-after { z-index: 10; }

.twentytwenty-handle {
	height: 38px;
	width: 38px;
	position: absolute;
	left: 50%;
	top: 50%;
	margin-left: -22px;
	margin-top: -22px;
	border: 3px solid white;
	-webkit-border-radius: 1000px;
	-moz-border-radius: 1000px;
	border-radius: 1000px;
	background-color: #FFF;
	-webkit-box-shadow: 0px 0px 12px rgba(51, 51, 51, 0.5);
	-moz-box-shadow: 0px 0px 12px rgba(51, 51, 51, 0.5);
	box-shadow: 0px 0px 12px rgba(51, 51, 51, 0.5);
	z-index: 40;
	cursor: pointer;
}

.twentytwenty-horizontal .twentytwenty-handle::before {
	bottom: 50%;
	margin-bottom: 22px;
	-webkit-box-shadow: 0 3px 0 white, 0px 0px 12px rgba(51, 51, 51, 0.5);
	-moz-box-shadow: 0 3px 0 white, 0px 0px 12px rgba(51, 51, 51, 0.5);
	box-shadow: 0 3px 0 white, 0px 0px 12px rgba(51, 51, 51, 0.5);
}

.twentytwenty-horizontal .twentytwenty-handle::after {
	top: 50%;
	margin-top: 22px;
	-webkit-box-shadow: 0 -3px 0 white, 0px 0px 12px rgba(51, 51, 51, 0.5);
	-moz-box-shadow: 0 -3px 0 white, 0px 0px 12px rgba(51, 51, 51, 0.5);
	box-shadow: 0 -3px 0 white, 0px 0px 12px rgba(51, 51, 51, 0.5);
}

.twentytwenty-left-arrow {
	border-right: 6px solid #999;
	left: 50%;
	margin-left: -17px;
}

.twentytwenty-right-arrow {
	border-left: 6px solid #999;
	right: 50%;
	margin-right: -17px;
}
/* twentytwenty-horizontal End */