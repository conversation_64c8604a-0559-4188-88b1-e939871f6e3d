/*-----------------------------------------------------------------------------------

	Shortcodes: headings.scss

-----------------------------------------------------------------------------------*/

$titular_title-prefix: titular;
$title_block-prefix: title-block;
$heading_block-prefix: heading-block;
$emphasis_title-prefix: emphasis-title;
$fancy_title-prefix: fancy-title;
$before_heading-prefix: before-heading;

/* Titular
-----------------------------------------------------------------*/

.#{$titular_title-prefix}-title {
	--#{$cnvs-prefix}titular-title-font-size-h1       :          #{$titular-title-font-size-h1};
	--#{$cnvs-prefix}titular-title-font-size-h2       :          #{$titular-title-font-size-h2};
	--#{$cnvs-prefix}titular-title-font-size-h3       :          #{$titular-title-font-size-h3};
	--#{$cnvs-prefix}titular-title-font-size-subtitle :          #{$titular-title-font-size-subtitle};
	font-weight: 500;
	letter-spacing: -1px;
	@at-root h1#{&} {
		font-size: var(--#{$cnvs-prefix}titular-title-font-size-h1);
	}
	@at-root h2#{&} {
		font-size: var(--#{$cnvs-prefix}titular-title-font-size-h2);
	}
	@at-root h3#{&} {
		font-size: var(--#{$cnvs-prefix}titular-title-font-size-h3);
	}
}

.titular-sub-title {
	margin: 0;
	font-weight: 600;
	letter-spacing: 4px;
	font-size: var(--#{$cnvs-prefix}titular-title-font-size-subtitle);
	text-transform: uppercase;
	.titular-title + & {
		margin-top: -25px;
		margin-bottom: 30px;
	}
}

/* Block Titles
-----------------------------------------------------------------*/
.#{$title-block-prefix} {
	--#{$cnvs-prefix}title-block-padding      :  #{$title-block-padding};
	--#{$cnvs-prefix}title-block-color        :  #{$title-block-color};
	--#{$cnvs-prefix}title-block-border       :  #{$title-block-border};
	--#{$cnvs-prefix}title-block-border-color :  #{$title-block-border-color};
	--#{$cnvs-prefix}title-block-subtitle-color :  #{$title-block-subtitle-color};

	padding: 2px 0 3px var(--#{$cnvs-prefix}title-block-padding);
	border-left: var(--#{$cnvs-prefix}title-block-border) solid var(--#{$cnvs-prefix}title-block-border-color);
	margin-bottom: 30px;
	&-right {
		padding: 2px var(--#{$cnvs-prefix}title-block-padding) 3px 0;
		border-left: 0;
		border-right: var(--#{$cnvs-prefix}title-block-border) solid var(--#{$cnvs-prefix}title-block-border-color);
		text-align: right;
	}
	h1,
	h2,
	h3,
	h4 {
		margin-bottom: 0;
	}

	> span {
		display: block;
		margin-top: 4px;
		color: var(--#{$cnvs-prefix}title-block-subtitle-color);
		font-weight: 300;
	}

	h1 + span,
	h2 + span {
		font-size: 1.25rem;
	}
	h3 + span {
		font-size: 1.125rem;
	}
	h4 + span {
		font-size: 0.9375rem;
	}
}

/* Heading Block - with Subtitle
-----------------------------------------------------------------*/

.#{$heading_block-prefix} {
	// Heading Title Block
	--#{$cnvs-prefix}heading-block-font-size-h1     :         #{$heading-block-font-size-h1};
	--#{$cnvs-prefix}heading-block-font-size-h2     :         #{$heading-block-font-size-h2};
	--#{$cnvs-prefix}heading-block-font-size-h3     :         #{$heading-block-font-size-h3};
	--#{$cnvs-prefix}heading-block-font-size-h4     :         #{$heading-block-font-size-h4};
	--#{$cnvs-prefix}heading-block-font-weight      :         #{$heading-block-font-weight};
	--#{$cnvs-prefix}heading-block-font-spacing     :         #{$heading-block-font-spacing};
	--#{$cnvs-prefix}heading-block-font-color       :         #{$heading-block-font-color};
	--#{$cnvs-prefix}heading-block-font-transform   :         #{$heading-block-font-transform};
	--#{$cnvs-prefix}heading-block-center-container :         #{$heading-block-center-container};
	--#{$cnvs-prefix}title-block-border-size        :         #{$title-block-border-size};
	--#{$cnvs-prefix}heading-block-border           :         #{$heading-block-border};
	--#{$cnvs-prefix}heading-block-span-color       :         #{$heading-block-span-color};

	margin-bottom: 50px;
	h1,
	h2,
	h3,
	h4 {
		margin-bottom: 0;
		font-weight: var(--#{$cnvs-prefix}heading-block-font-weight);
		text-transform: var(--#{$cnvs-prefix}heading-block-font-transform);
		letter-spacing: var(--#{$cnvs-prefix}heading-block-font-spacing);
		color: var(--#{$cnvs-prefix}heading-block-font-color);
	}

	h1 {
		font-size: var(--#{$cnvs-prefix}heading-block-font-size-h1);
	}
	h2 {
		font-size: var(--#{$cnvs-prefix}heading-block-font-size-h2);
	}
	h3 {
		font-size: var(--#{$cnvs-prefix}heading-block-font-size-h3);
	}
	h4 {
		font-size: var(--#{$cnvs-prefix}heading-block-font-size-h4);
	}

	> span:not(.#{$before_heading-prefix}) {
		display: block;
		margin-top: 0.5rem;
		font-weight: 300;
		color: var(--#{$cnvs-prefix}heading-block-span-color);
	}

	// .#{$before_heading-prefix} {
	// 	margin-bottom: 0.5rem;
	// }

	&.center > span,
	&.text-center > span,
	.center & > span,
	.text-center & > span {
		max-width: var(--#{$cnvs-prefix}heading-block-center-container);
		margin-left: auto;
		margin-right: auto;
	}

	@include media-breakpoint-up(md) {
		.text-md-start & > span {
			max-width: none !important;
		}
	}

	h1 + span {
		font-size: calc(var(--#{$cnvs-prefix}heading-block-font-size-h1) / 1.5);
	}
	h2 + span,
	h3 + span,
	h4 + span {
		font-size: calc(var(--#{$cnvs-prefix}heading-block-font-size-h2) / 1.75);
	}

	&::after {
		content: "";
		display: block;
		margin-top: 30px;
		width: var(--#{$cnvs-prefix}title-block-border-size);
		border-top: var(--#{$cnvs-prefix}heading-block-border);
	}

	.center &::after,
	.text-center &::after,
	&.center::after,
	&.text-center::after {
		margin: 30px auto 0;
	}

	@include media-breakpoint-up(md) {
		.text-md-start &::after {
			margin-left: 0 !important;
		}
	}

	.text-end &,
	&.text-end,
	&.title-right {
		direction: rtl;
	}

	&.border-0::after,
	&.border-bottom-0::after {
		display: none;
	}

	&.border-color::after {
		border-color: var(--#{$cnvs-prefix}themecolor);
	}
}

/* Emphasis Title
-----------------------------------------------------------------*/

.#{$emphasis_title-prefix} {
	--#{$cnvs-prefix}emphasis-title-font-size :          #{$emphasis-title-font-size};
	margin: 0 0 50px;
	h1,
	h2 {
		font-weight: 700;
		text-transform: uppercase;
		color: var(--#{$cnvs-prefix}contrast-900);
		font-weight: 400;
		text-transform: none;
		font-size: var(--#{$cnvs-prefix}emphasis-title-font-size);
		letter-spacing: -2px;
		strong {
			font-weight: $heading-block-font-weight;
		}
	}
}

/* Justify Border Title
-----------------------------------------------------------------*/

.#{$fancy_title-prefix} {
	--#{$cnvs-prefix}fancy-title-bg            :          #{$fancy-title-bg};
	--#{$cnvs-prefix}fancy-title-padding       :          #{$fancy-title-padding};
	--#{$cnvs-prefix}fancy-title-border-size   :          #{$fancy-title-border-size};
	--#{$cnvs-prefix}fancy-title-border-color  :          #{$fancy-title-border-color};
	--#{$cnvs-prefix}fancy-title-dotted-border :          #{$fancy-title-dotted-border};
	position: relative;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-align: center;
	align-items: center;
	margin-bottom: 2rem;

	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		position: relative;
		margin-bottom: 0;
	}

	&::before,
	&::after {
		content: "";
		-ms-flex-preferred-size: 0;
		flex-basis: 0;
		-ms-flex-positive: 1;
		flex-grow: 1;
		max-width: 100%;
		height: 0;
		border-top: calc(var(--#{$cnvs-prefix}fancy-title-border-size) * 3) double $line-color;
	}

	&::before {
		display: none;
		margin-right: var(--#{$cnvs-prefix}fancy-title-padding);
	}

	&::after {
		margin-left: var(--#{$cnvs-prefix}fancy-title-padding);
	}
}

.title-border::before,
.title-border::after {
	border-top-width: var(--#{$cnvs-prefix}fancy-title-border-size);
	border-top-style: solid;
}

.title-border-color::before,
.title-border-color::after {
	border-top: var(--#{$cnvs-prefix}fancy-title-border-size) solid
		var(--#{$cnvs-prefix}themecolor);
}

/* Fancy Title - Center Align
-----------------------------------------------------------------*/

.title-center::before {
	display: block;
}

/* Fancy Title - Right Align
-----------------------------------------------------------------*/

.title-right {
	&::before {
		display: block;
	}
	&::after {
		display: none;
	}
}

/* Fancy Title - Bottom Short Border
-----------------------------------------------------------------*/
.title-bottom-border {
	&::before,
	&::after {
		display: none;
	}
	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		width: 100%;
		padding: 0 0 var(--#{$cnvs-prefix}fancy-title-padding);
		border-bottom: calc(var(--#{$cnvs-prefix}fancy-title-border-size) * 2) solid $title-block-border-color;
	}
}

/* Sub Heading
-----------------------------------------------------------------*/
:root {
	--#{$cnvs-prefix}before-heading-font-size: #{$before-heading-font-size};
	--#{$cnvs-prefix}before-heading-font-weight: #{$before-heading-font-weight};
	--#{$cnvs-prefix}before-heading-font-family: #{$before-heading-font-family};
	--#{$cnvs-prefix}before-heading-ls: #{$before-heading-ls};
	--#{$cnvs-prefix}before-heading-tt: #{$before-heading-tt};
	--#{$cnvs-prefix}before-heading-fst: normal;
	--#{$cnvs-prefix}before-heading-color: var(--#{$cnvs-prefix}contrast-600);
	--#{$cnvs-prefix}before-heading-margin-bottom: 0.25rem;
}

.#{$before_heading-prefix} {
	display: block;
	margin: 0 0 var(--#{$cnvs-prefix}before-heading-margin-bottom);
	font-size: var(--#{$cnvs-prefix}before-heading-font-size);
	font-weight: var(--#{$cnvs-prefix}before-heading-font-weight);
	font-family: var(--#{$cnvs-prefix}before-heading-font-family);
	font-style: normal;
	letter-spacing: var(--#{$cnvs-prefix}before-heading-ls);
	text-transform: var(--#{$cnvs-prefix}before-heading-tt);
	color: var(--#{$cnvs-prefix}before-heading-color);
}
