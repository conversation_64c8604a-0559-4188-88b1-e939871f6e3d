
/* ----------------------------------------------------------------
	extras.scss
-----------------------------------------------------------------*/


/* ----------------------------------------------------------------
	Bootstrap Specific
-----------------------------------------------------------------*/

.table {
	.success { color: var(--#{$prefix}success); }
	.info { color: var(--#{$prefix}info); }
	.warning { color: var(--#{$prefix}warning); }
	.danger { color: var(--#{$prefix}danger); }
}

ul:not(.pagination) .page {
	&-link {
		&,
		&:hover,
		&:focus {
			color: var(--#{$cnvs-prefix}themecolor);
		}
	}
}

.boot-tabs { margin-bottom: 20px !important; }

.carousel-control {
	.fa-chevron-left,
	.fa-chevron-right {
		position: absolute;
		top: 47.5%;
		z-index: 5;
		display: inline-block;
	}
	.fa-chevron-left { left: 50%; }
	.fa-chevron-right { right: 50%; }
}

.responsive-utilities th small {
	display: block;
	font-weight: 400;
	color: var(--#{$cnvs-prefix}contrast-500);
}

.show-grid [class^=col-] {
	padding-top: 10px;
	padding-bottom: 10px;
	border: 1px solid rgba(var(--#{$cnvs-prefix}contrast-rgb), .15);
	background-color: rgba(var(--#{$cnvs-prefix}contrast-rgb), .1);
}

.img-circle { border-radius: 50% !important; }

ul:not(.pagination) .page-item.active .page-link,
ul:not(.pagination) .page-link:hover,
ul:not(.pagination) .page-link:focus {
	color: var(--#{$cnvs-prefix}contrast-0) !important;
	background-color: var(--#{$cnvs-prefix}themecolor) !important;
	border-color: var(--#{$cnvs-prefix}themecolor) !important;
}

.nav-pills {
	> li.active > {
		a,
		a:hover,
		a:focus { background-color: var(--#{$cnvs-prefix}themecolor); }
	}
}

label{
	&.radio,
	&.checkbox { padding-left: 20px; }
}

.input-group-lg > .input-group-addon { line-height: 1; }



/* ----------------------------------------------------------------
	Shape Dividers
-----------------------------------------------------------------*/

.shape-divider {
	position: absolute;
	top: -1px;
	left: 0;
	width: 100%;
	overflow: hidden;
	z-index: 1;

	&[data-position="bottom"] {
		top: auto;
		bottom: -1px;
	}

	&[data-front="true"] {
		z-index: 4;
	}

	svg {
		position: relative;
		width: 100%;
		width: calc( 100% + 1.3px );
		height: 50px;
		left: 50%;
		transform: translateX(-50%);
		opacity: 0;
	}
	&[data-position="bottom"],
	&[data-flip-vertical="true"] {
		transform: rotate(180deg);
	}

	&[data-position="bottom"][data-flip-vertical="true"] {
		transform: rotate(0deg);
	}

	&[data-flip="true"] svg {
		transform: translateX(-50%) rotateY(180deg);
	}

	&-fill {
		fill: var(--#{$cnvs-prefix}body-bg);
		transform-origin: center;
		transform: rotateY(0deg);
	}
}


/* ----------------------------------------------------------------
	HTML5 Youtube Video Backgrounds
-----------------------------------------------------------------*/

.mb_YTVPlayer {
	display:block;
	transform:translateZ(0);
	transform-style: preserve-3d;
	perspective: 1000;
	backface-visibility: hidden;
	transform:translate3d(0,0,0);
	animation-timing-function: linear;
	&:focus { outline: 0; }
	&.fullscreen {
		display:block!important;
		position: fixed!important;
		width: 100%!important;
		height: 100%!important;
		top: 0!important;
		left: 0!important;
		margin: 0!important;
		border: none !important;
		.mbYTP_wrapper { opacity:1 !important; }
	}
}

.mbYTP_wrapper iframe { max-width: 4000px !important; }

.inline_YTPlayer {
	margin-bottom: 20px;
	vertical-align:top;
	position:relative;
	left:0;
	overflow: hidden;
	border-radius: 4px;
	box-shadow: 0 0 5px rgba(0,0,0,.7);
	background: rgba(0,0,0,.5);
	img {
		border: none!important;
		-moz-transform: none!important;
		-webkit-transform: none!important;
		-o-transform: none!important;
		transform: none!important;
		margin:0!important;
		padding:0!important
	}
}


/* ----------------------------------------------------------------
	Datepicker Component
-----------------------------------------------------------------*/

.datepicker {
	td,
	th { padding: 5px; }
}

/* ----------------------------------------------------------------
	Star Ratings Component
-----------------------------------------------------------------*/

.rating {
	&-xl { font-size: 3rem !important; }
	&-lg { font-size: 2.5rem !important; }
	&-md { font-size: 2rem !important; }
	&-sm { font-size: 1.75rem !important; }
	&-xs { font-size: 1.25rem !important; }
	&-container .star { margin: 0 2px !important; }
	&-container .filled-stars {
		-webkit-text-stroke: 0px !important;
		text-shadow: none !important;
	}
}

/* ----------------------------------------------------------------
	Custom Animate.css Styles
-----------------------------------------------------------------*/

@include keyframes (fadeInDownSmall) {
	from {
	opacity: 0;
	-webkit-transform: translate3d(0, -50%, 0);
	transform: translate3d(0, -50%, 0);
	}

	to {
	opacity: 1;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	}
}

.fadeInDownSmall {
	-webkit-animation-name: fadeInDownSmall;
	animation-name: fadeInDownSmall;
}


@include keyframes (fadeInLeftSmall) {
	from {
	opacity: 0;
	-webkit-transform: translate3d(-50%, 0, 0);
	transform: translate3d(-50%, 0, 0);
	}

	to {
	opacity: 1;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	}
}

.fadeInLeftSmall {
	-webkit-animation-name: fadeInLeftSmall;
	animation-name: fadeInLeftSmall;
}


@include keyframes (fadeInRightSmall) {
	from {
	opacity: 0;
	-webkit-transform: translate3d(50%, 0, 0);
	transform: translate3d(50%, 0, 0);
	}

	to {
	opacity: 1;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	}
}

.fadeInRightSmall {
	-webkit-animation-name: fadeInRightSmall;
	animation-name: fadeInRightSmall;
}


@include keyframes (fadeInUpSmall) {
	from {
	opacity: 0;
	-webkit-transform: translate3d(0, 50%, 0);
	transform: translate3d(0, 50%, 0);
	}

	to {
	opacity: 1;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	}
}

.fadeInUpSmall {
	-webkit-animation-name: fadeInUpSmall;
	animation-name: fadeInUpSmall;
}


@include keyframes (fadeOutDownSmall) {
	from {
	opacity: 1;
	}

	to {
	opacity: 0;
	-webkit-transform: translate3d(0, 50%, 0);
	transform: translate3d(0, 50%, 0);
	}
}

.fadeOutDownSmall {
	-webkit-animation-name: fadeOutDownSmall;
	animation-name: fadeOutDownSmall;
}


@include keyframes (fadeOutLeftSmall) {
	from {
	opacity: 1;
	}

	to {
	opacity: 0;
	-webkit-transform: translate3d(-50%, 0, 0);
	transform: translate3d(-50%, 0, 0);
	}
}

.fadeOutLeftSmall {
	-webkit-animation-name: fadeOutLeftSmall;
	animation-name: fadeOutLeftSmall;
}


@include keyframes (fadeOutRightSmall) {
	from {
	opacity: 1;
	}

	to {
	opacity: 0;
	-webkit-transform: translate3d(50%, 0, 0);
	transform: translate3d(50%, 0, 0);
	}
}

.fadeOutRightSmall {
	-webkit-animation-name: fadeOutRightSmall;
	animation-name: fadeOutRightSmall;
}

@include keyframes (fadeOutUpSmall) {
	from {
	opacity: 1;
	}

	to {
	opacity: 0;
	-webkit-transform: translate3d(0, -50%, 0);
	transform: translate3d(0, -50%, 0);
	}
}

.fadeOutUpSmall {
	-webkit-animation-name: fadeOutUpSmall;
	animation-name: fadeOutUpSmall;
}
