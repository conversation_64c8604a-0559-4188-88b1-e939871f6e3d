/* ----------------------------------------------------------------
	CSS Specific to the One Page Module

	Some of the CSS Codes have been modified from the Original
	style.css File to match the Minimal Styling of this Module
-----------------------------------------------------------------*/

/* ----------------------------------------------------------------------------
	FONTS
---------------------------------------------------------------------------- */
:root {
	--cnvs-body-font: 'Roboto', sans-serif;
	--cnvs-primary-font: 'Source Sans Pro', sans-serif;
	--cnvs-header-height: 70px;
}

#header {
	--cnvs-sticky-header-height: 70px;
	--cnvs-header-transparent-border-color: rgba(var(--cnvs-contrast-rgb), .05);
	--cnvs-primary-menu-font-weight: 400;
	--cnvs-primary-menu-font-size: 0.75rem;
	--cnvs-primary-menu-tt: uppercase;
	--cnvs-primary-menu-ls: 3px;
}

.is-expanded-menu #header.transparent-header:not(.border-full-header):not(.sticky-header) #header-wrap {
	border-bottom: transparent;
}

.is-expanded-menu .menu-link {
	--cnvs-primary-menu-padding-x: 22px;
}

/* ----------------------------------------------------------------------------
	Header
---------------------------------------------------------------------------- */

.is-expanded-menu #header.full-header #logo {
	border-right: 0;
}

.is-expanded-menu #header.full-header .primary-menu > .menu-container {
	padding-right: 0;
	margin-right: 0;
	border-right: 0;
}

.is-expanded-menu #header.show-on-sticky #header-wrap {
	opacity: 0;
	pointer-events: none;
}

.is-expanded-menu #header.show-on-sticky.sticky-header #header-wrap {
	opacity: 1;
	pointer-events: auto;
}


/* Sticky Header
---------------------------------------------------------------------------- */

.is-expanded-menu #header.sticky-header #header-wrap,
.is-expanded-menu #header.full-header.border-full-header.sticky-header #header-wrap {
	box-shadow: none;
}


/* Side Panel
---------------------------------------------------------------------------- */

body.side-push-panel.stretched #header .container {
	right: 0;
}

body.side-push-panel.side-panel-open.stretched #header .container {
	right: 300px;
}

body.side-panel-left.side-push-panel.stretched #header .container {
	left: 0;
	right: auto;
}

body.side-panel-left.side-push-panel.side-panel-open.stretched #header .container { left: 300px; }


/* ----------------------------------------------------------------------------
	Page Section
---------------------------------------------------------------------------- */

.page-section {
	padding: 120px 0;
}


/* ----------------------------------------------------------------------------
	Heading Block
---------------------------------------------------------------------------- */

.heading-block h2 {
	--cnvs-heading-block-font-weight: 500;
	--cnvs-heading-block-font-spacing: 3px;
	font-family: var(--cnvs-body-font);
}


/* ----------------------------------------------------------------------------
	Portfolio Description
---------------------------------------------------------------------------- */

.bg-overlay .portfolio-desc h3 {
	--cnvs-portfolio-desc-title-size: 22px;
	font-weight: 300;
	text-transform: uppercase;
	letter-spacing: 2px;
}

.bg-overlay .portfolio-desc h3 a {
	color: #333 !important;
	text-shadow: none;
}

.bg-overlay .portfolio-desc span {
	margin-top: 12px;
	text-transform: uppercase;
	font-size: 14px;
	letter-spacing: 1px;
	font-weight: 300;
}

.bg-overlay .portfolio-desc span a {
	color: #999 !important;
	text-shadow: none;
}


/**/
.op-gradient-icon {
	background: rgb(131,58,180);
	background: linear-gradient(90deg, rgba(131,58,180,1) 0%, rgba(253,29,29,1) 50%, rgba(252,176,69,1) 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}


/* ----------------------------------------------------------------------------
	Buttons
---------------------------------------------------------------------------- */

.button.button-border {
	font-weight: 400;
	letter-spacing: 2px;
	font-size: 13px;
}

.button.button-border.button-large {
	font-size: 15px;
}

.button.button-border.button-light {
	border-color: #F9F9F9;
	color: #F9F9F9;
}

.button.button-border.button-light:hover {
	background-color: #F9F9F9;
	color: #333;
	text-shadow: none;
}


/* Blog
---------------------------------------------------------------------------- */

.posts-md .entry-title h3 {
	font-size: 1.375rem;
	letter-spacing: 0;
}


/* More Link
---------------------------------------------------------------------------- */

.more-link {
	font-weight: 400;
	text-decoration-thickness: 1px;
	font-style: normal;
}

.more-link i.uil-angle-right-b {
	position: relative;
	top: 1px;
	margin-left: 2px;
}


.link-border {
	font-weight: 600;
	border-bottom: 3px solid var(--cnvs-themecolor);
	-webkit-transition: all .3s ease;
	-o-transition: all .3s ease;
	transition: all .3s ease;
}

.link-border:hover {
	border-color: #FFF;
}


/* ----------------------------------------------------------------------------
	Team & Testimonials
---------------------------------------------------------------------------- */

.team-title h4 {
	--cnvs-team-title-font-size: 17px;
	font-weight: 600;
	letter-spacing: 2px;
}

.team-title span {
	--cnvs-team-title-designation-font-family: var(--cnvs-body-font);
	--cnvs-team-title-designation-font-size: 14px;
	--cnvs-team-title-designation-font-style: normal;
	--cnvs-team-title-designation-font-color: #999;
	text-transform: uppercase;
	letter-spacing: 1px;
	margin-top: 2px;
}

.team-social-icons {
	position: absolute;
	width: 100%;
	bottom: 20px;
}

.parallax .fslider * {
	-webkit-backface-visibility: hidden !important;
}

.parallax .fslider .flex-control-paging * { 
	-webkit-backface-visibility: visible !important;
}

.parallax .testimonial .testi-content p {
	line-height: 1.6 !important;
	font-size: 24px;
	letter-spacing: 1px;
	font-weight: 300;
	font-family: var(--cnvs-primary-font);
	font-style: normal;
}

.parallax .testimonial .testi-meta {
	margin-top: 20px;
	font-size: 15px;
	letter-spacing: 2px;
	font-weight: 500;
}

.parallax .testimonial .testi-meta span {
	font-weight: 300;
	font-size: 14px;
	letter-spacing: 1px;
}


/* Border Form Design
---------------------------------------------------------------------------- */

.form-control.border-form-control {
	--cnvs-input-btn-padding-y : 8px;
	--cnvs-input-btn-font-size : 21px;
	--cnvs-input-font-family: var(--cnvs-primary-font);
	letter-spacing: 1px;
}

.border-form-control::-moz-placeholder { font-weight: 300; }
.border-form-control:-ms-input-placeholder { font-weight: 300; }
.border-form-control::-webkit-input-placeholder { font-weight: 300; }

textarea.border-form-control {
	resize: none;
	overflow: hidden;
	word-wrap: break-word;
}

/* ----------------------------------------------------------------------------
	Footer Widgets
---------------------------------------------------------------------------- */

#footer .widget {
	padding: 30px 0;
	--cnvs-widget-title-margin: 50px;
	--cnvs-widget-title-font-size: 20px;
	--cnvs-widget-title-font-weight: 300;
	--cnvs-widget-title-letter-spacing: 3px;
	--cnvs-widget-title-text-transform: uppercase;
}

#footer .widget ul.footer-site-links li {
	margin: 5px 0;
	font-size: 17px;
	letter-spacing: 1px;
	font-weight: 300;
}


#footer .widget .form-control { text-align: center; }

.widget p.lead {
	font-size: 17px;
	letter-spacing: 1px;
	line-height: 1.6;
}


/* ----------------------------------------------------------------------------
	One Page Module: Slider Specifics
---------------------------------------------------------------------------- */

.one-page-arrow i {
	-webkit-animation-duration: 1.5s !important;
	animation-duration: 1.5s !important;
}


/* Large One Word in Slider
---------------------------------------------------------------------------- */

.opm-large-word { font-size: 116px; }

.device-md .opm-large-word { font-size: 96px; }

.device-sm .opm-large-word { font-size: 72px; }

.device-xs .opm-large-word { font-size: 54px; }


/* Medium One Word in Slider
---------------------------------------------------------------------------- */

.opm-medium-word { font-size: 96px; }

.device-md .opm-medium-word { font-size: 80px; }

.device-sm .opm-medium-word { font-size: 60px; }

.device-xs .opm-medium-word { font-size: 48px; }


/* Medium Before Heading
---------------------------------------------------------------------------- */

.before-heading.opm-medium {
	font-size: 30px;
	font-style: normal;
	margin: 0 0 0 5px;
	line-height: 1;
	letter-spacing: 12px;
}

.device-md .before-heading.opm-medium { font-size: 22px; }

.device-sm .before-heading.opm-medium { font-size: 18px; }

.device-xs .before-heading.opm-medium { font-size: 14px; }


/* Large Counter in Slider
---------------------------------------------------------------------------- */

.opm-large-counter {
	font-size: 480px;
	color: #F2F2F2;
	line-height: 1;
}

.device-md .opm-large-counter { font-size: 360px; }

.device-sm .opm-large-counter { font-size: 220px; }

.device-xs .opm-large-counter { font-size: 150px; }


/* Slider Grid Blocks
---------------------------------------------------------------------------- */

.videoplay-on-hover h2 {
	font-size: 40px;
	font-weight: bold;
}

.device-sm .videoplay-on-hover h2 { font-size: 32px; }

.device-xs .videoplay-on-hover h2 { font-size: 26px; }



/* Content Switch
---------------------------------------------------------------------------- */


.con-switch {
	position: relative;
	display: block;
	overflow: hidden;
	-webkit-backface-visibility: hidden;
}

.con-switch .con-default,
.con-switch .con-show {
	display: block;
	opacity: 1;
	-webkit-transition: opacity .4s ease;
	-o-transition: opacity .4s ease;
	transition: opacity .4s ease;
}

.con-switch .con-show {
	position: absolute;
	opacity: 0;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: 1;
	text-align: center;
}

.con-switch:hover .con-show { opacity: 1; }

.con-switch:hover .con-default { opacity: 0; }



/* Hero Video Play Event
---------------------------------------------------------------------------- */

.big-video-button {
	display: inline-block;
	width: 64px;
	height: 64px;
	line-height: 64px;
	border-radius: 50%;
	background-color: #333;
	color: #F5F5F5 !important;
	font-size: 24px;
	text-align: center;
	text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
	opacity: 1;
	-webkit-transition: opacity .3s ease;
	-o-transition: opacity .3s ease;
	transition: opacity .3s ease;
}

.big-video-button:hover {
	opacity: 0.9;
}

.dark .big-video-button,
.big-video-button.dark {
	background-color: #F5F5F5;
	color: #333 !important;
	text-shadow: none;
}

.big-video-button i:nth-of-type(1) {
	position: relative;
	left: 2px;
}

.big-video-button i:nth-of-type(2),
.big-video-button.video-played i:nth-of-type(1) { display: none; }

.big-video-button.video-played i:nth-of-type(2) { display: block; }

.big-video-button.video-played { opacity: 0.1; }

#slider:hover .big-video-button.video-played { opacity: 1; }


.slider-caption h2 {
	font-size: 78px;
	letter-spacing: 3px;
	text-transform: lowercase;
}

.slider-caption p {
	font-weight: 300;
	text-transform: lowercase;
	font-size: 26px;
	line-height: 1.8;
	max-width: 600px;
	margin-top: 50px;
	margin-left: auto;
	margin-right: auto;
}



.testimonials-lg { overflow: visible; }

.testimonials-lg:before {
	position: absolute;
	top: -20px;
	left: -30px;
	content: "“";
	color: rgba(0,0,0,0.07);
	font-size: 144px;
	line-height: 1;
	font-family: 'Passion One';
}

.testimonials-lg .testi-content p {
	font-size: 26px;
	line-height: 1.6;
	font-style: normal;
	font-weight: 300;
	font-family: var(--cnvs-body-font);
	color: #111;
}

.testimonials-lg .testi-content p:before,
.testimonials-lg .testi-content p:after { content:''; }

.testimonials-lg .testi-meta {
	font-size: 14px;
	font-weight: 400;
	margin-top: 30px;
	color: #999;
}

.testimonials-lg .testi-meta span {
	font-size: 13px;
	font-weight: 300;
	color: #BBB;
}

/* ----------------------------------------------------------------------------
	Responsive
---------------------------------------------------------------------------- */


@media (max-width: 991px) {

	#header.transparent-header:not(.sticky-header) #header-wrap {
		background-color: #FFF;
		border-bottom: 1px solid #EEE;
	}

	#header.transparent-header.dark:not(.sticky-header) #header-wrap {
		background-color: #333;
		border-bottom-color: rgba(255,255,255,0.1) !important;
	}

	.dark #header.transparent-header #header-wrap,
	.dark #header.semi-transparent #header-wrap,
	#header.dark.transparent-header #header-wrap,
	#header.dark.semi-transparent #header-wrap {
		background-color: #333;
		border-bottom-color: rgba(255,255,255,0.1);
	}

}

