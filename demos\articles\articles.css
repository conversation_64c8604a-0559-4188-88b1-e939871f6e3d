/* ----------------------------------------------------------------
	Canvas: Article
-----------------------------------------------------------------*/


:root {
	--cnvs-body-font:				'Nunito', sans-serif;
	--cnvs-primary-font:			'Playfair Display', serif;
	--cnvs-themecolor:  			#0C1CCC;
	--cnvs-themecolor-rgb: 			12, 28, 204;
	--cnvs-fulldark: 				#111;
	--cnvs-section-about-bg: 		url('images/dots-1.png');
	--cnvs-section-features-bg: 	url('images/bg-3.svg');
}

/* Swiper Slider
---------------------------------------------------------------------------- */

.swiper_wrapper .swiper-container { background: transparent; }

.slider-element .heading-block h2 {
	font-size: 2.2rem;
	line-height: 1.4;
}

.slider-arrow-left,
.slider-arrow-right {
	border-radius: 50%;
	left: 20px;
	transition: all .3s ease;
	background-color: var(--bs-dark);
}

.slider-arrow-right {
	left: auto;
	right: 20px;
}

.slider-arrow-left:hover,
.slider-arrow-right:hover {
	transform: translateY(-50%) scale(1.15);
	background-color: var(--cnvs-themecolor) !important;
}

.swiper-button-disabled {
	opacity: 0;
	z-index: -99;
}

.slider-arrow-left i,
.slider-arrow-right i {
	color: #FFF;
	font-size: 26px;
}

.animated.faster {
	-webkit-animation-duration: 600ms;
	animation-duration: 600ms;
}

.device-md .slider-element .article-info { max-width: 98% }
.slider-element .article-info { max-width: 85% }

.button-dark { background-color: var(--bs-dark); }

.fbox-content h3 { font-size: 1.5em; }

.article-info h5 {
	font-weight: 400;
	font-family: var(--cnvs-body-font);
	color: #AAA;
	margin-bottom: 0;
}

.article-info h5 a { font-family: var(--cnvs-primary-font); }

.rating-stars {
	position: relative;
	top: 2px;
}

.article-price del { color: #BBB }
body:not(.dark) .article-price { color: var(--cnvs-themecolor) }


/* elements
---------------------------------------------------------------------------- */
.badge {
	position: relative;
	padding: 0.25em 0.4em;
	font-weight: 500;
	border-radius: 2px;
	top: -1px;
}

.before-heading {
	font-style: normal;
	font-weight: 400;
	font-size: 1rem;
}

.heading-block h2 { font-size: 240%; }

.heading-block::after {
	opacity: .8;
	border-width: 2px;
	border-style: solid;
	border-color: var(--cnvs-themecolor);
}

.section-features .btn-link { font-size: 14px; }

.section-features .card {
	border: 0;
	overflow: hidden;
	background: #FFF;
  	background-clip: padding-box;
	box-shadow: 0 0 35px rgba(140, 152, 164, 0.2);
}

.section-features .card::before {
	content: '';
    position: absolute;
    top: auto;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    margin-bottom: 0;
    height: 3px;
    width: 100%;
    background: linear-gradient(to right, blue, lightblue);
}

.section-features .card.border-color-pink::before { background: linear-gradient(to right, red, pink); }
.section-features .card.border-color-yellow::before { background: linear-gradient(to right, teal, cyan); }
.section-features .card.border-color-info::before { background: linear-gradient(to right, #111, #DDD); }

body:not(.dark) .section-scroller {
	background-image: linear-gradient(to top, rgba(241,244,248,.5), rgba(241,244,248,.5) 30%, #FFF 30%);
	background-repeat: no-repeat;
}

body:not(.dark) .section-features {
	background-color: transparent;
	background-repeat: no-repeat;
	background-position: center bottom;
	background-image: var(--cnvs-section-features-bg);
	background-size: auto 100%;
}
.section-scroller .swiper-container,
.section-scroller .swiper-container .swiper-slide { overflow: visible; }

.device-xl .section-scroller .swiper-container .swiper-slide { width: 38%; }
.device-lg .section-scroller .swiper-container .swiper-slide { width: 42%; }
.device-md .section-scroller .swiper-container .swiper-slide { width: 58%; }
.device-sm .section-scroller .swiper-container .swiper-slide { width: 42%; }
.device-xs .section-scroller .swiper-container .swiper-slide { width: 65%; }

.section-scroller .card {
	border: 1px solid var(--bs-light);
	border-left-width: 1px;
	border-top-left-radius: 0;
	border-top-right-radius: 0;
	box-shadow: 0 0 10px rgba(140, 152, 164, 0.15);
	margin-top: -20px;
}

body:not(.dark) .section-scroller .card h4 a { color: var(--cnvs-fulldark); }

.section-scroller .card p {
	font-size: 13px;
	color: #888;
	overflow : hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 4;
	-webkit-box-orient: vertical;
}

.section-scroller { overflow: visible; }

.swiper-container-horizontal>.swiper-scrollbar {
	width: 80%;
	max-width: 1140px;
	left: 50%;
	bottom: -60px;
	height: 8px;
	z-index: 1;
	background-color: rgba(0,0,0,0.045);
	transform: translateX(-50%);
}

body:not(.dark) .swiper-scrollbar-drag { opacity: .2; }

.circle-border {
	position: relative;
	margin-top: 25px;
	margin-left: 25px;
	border: 2px solid rgba(12, 28, 204, .05);
	border-radius: 50%;
	height: 250px;
	width: 250px;
}

.feature-content {
	position: absolute;
	top: -25px;
	left: -25px;
	width: 300px;
	height: 300px;
}

.circle-inner {
	border-radius: 50%;
	min-width: 80px;
	min-height: 80px;
	padding: 4px;
	background: #FFF;
	box-shadow: 0 0 35px rgba(140, 152, 164, 0.2);
	text-align: center;
	line-height: 1;
	box-sizing: content-box;
	white-space: nowrap;
}

.circle-inner::before {
	content: "";
	display: inline-block;
	vertical-align: middle;
	padding-top: 100%;
	height: 0;
}

.circle-inner > div {
	display: inline-block;
	vertical-align: middle;
}

.counter { font-size: 20px; }
.counter + h5 { font-size: 12px; }

.button { box-shadow:0 8px 16px rgba(32,41,50,0.12); }

.owl-dots {
	position: absolute;
	top: -48px;
	right: 0;
}

/* Border Form Design
---------------------------------------------------------------------------- */
.form-control.border-form-control {
	--cnvs-input-btn-padding-y : 		8px;
	--cnvs-input-btn-padding-x :		4px;
	--cnvs-input-btn-border-width:      2px;
	--cnvs-input-btn-border-color:      var(--bs-light);
}

/* Mode Switcher
---------------------------------------------------------------------------- */
.mode-switcher {
	position: fixed;
	right: 29px;
	bottom: 17%;
	z-index: 299;
}

.mode-switcher .pts-text,
.mode-switcher .pts-switcher {
	display: block;
	overflow: hidden;
}

.mode-switcher .pts-text {
	font-size: 14px;
	margin-bottom: 4px; 
	text-transform: uppercase;
	font-weight: 600;
}

.mode-switcher .pts-text span.tlight,
.mode-switcher.pts-switch-active .pts-text span.tdark { display: none; }

.mode-switcher .pts-text span.tdark,
.mode-switcher.pts-switch-active .pts-text span.tlight { display: inline-block; }

body:not(.dark) #footer { background-color: #FFF; }

.dark .owl-carousel .owl-dots .owl-dot,
body:not(.dark) #copyrights { background-color: var(--bs-light); }

@media (min-width: 992px) {

	.slider-element .heading-block h2 {
		font-size: 3.4rem;
		line-height: 1.2;
	}

	.slider-element .article-info { max-width: 80% }

	.services-info {
		 position: absolute;
		 top: 0;
		 left: 30px;
		 z-index: 9;
		 text-align: left !important;
	}

	.fbox-desc h3 {
		font-size: 20px;
		font-weight: 400;
		color: #000;
	}

	.fbox-desc span { font-size: 14px; }

	.section-scroller .card {
		border-left: 0;
		border-top-left-radius: 0;
		border-bottom-left-radius: 0;
		border-top-left-radius: 0.25rem;
		border-top-right-radius: 0.25rem;
		margin-top: 0;
		margin-bottom: 14px;
	}

	.section-scroller .card h4 { font-size: 150%; }

	.section-scroller img {
		position: relative;
		z-index: 1;
	}

	.sale-flash {
		position: absolute;
		left: auto;
		top: -10px;
		right: -10px;
	}

	.section-scroller:not(:hover) .slider-arrow-left-1,
	.section-scroller:not(:hover) .slider-arrow-right-1 { opacity: 0; }

	.circle-border {
		height: 400px;
		width: 400px;
	}

	.feature-content {
		top: -50px;
		left: -50px;
		width: 500px;
		height: 500px;
	}

	.circle-inner {
		min-width: 100px;
		min-height: 100px;
		padding: 10px;
	}

	.counter { font-size: 28px; }
	.counter + h5 { font-size: 13px; }

	.section-about::before {
	    content: '';
	    position: absolute;
	    bottom: 0;
	    left: 0;
		background-color: transparent;
	    background-image: var(--cnvs-section-about-bg);
		background-repeat: no-repeat;
		background-position: top right;
	    transform:  rotate(-180deg);
	    background-size: 40%;
	    width: 100%;
	    height: 100%;
	    z-index: 1;
	}

	.dark .section-about::before {
		opacity: 1;
		background: var(--bs-dark);
	}

}

.dark a { color: var(--bs-light); }
.dark svg { fill:var(--bs-light); }

.dark .section-features .card {
	background-color: var(--cnvs-fulldark);
	box-shadow: 0 0 20px rgba(255,255,255,0.02);
}

.dark .section-scroller .card {
	background-color: rgba(0, 0, 0, .3);
	border: 1px solid rgba(255,255,255,0.03);;
	border-left-width: 1px;
	border-top-left-radius: 0;
	border-top-right-radius: 0;
	box-shadow: 0;
	margin-top: -20px;
}
.dark .swiper-container-horizontal>.swiper-scrollbar { background-color: rgba(255,255,255,0.1); }

.dark .circle-inner {
	background: var(--bs-light);
	box-shadow: none;
}

.dark .circle-border { border-color: rgba(255, 255, 255, 0.1); }