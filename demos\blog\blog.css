/* ----------------------------------------------------------------
	Canvas: Blog
-----------------------------------------------------------------*/

/* change your menu color here */
:root {
	--cnvs-themecolor: #F39887;
	--cnvs-themecolor-rgb: 243, 152, 135;
	--cnvs-primary-font: 'Domine', serif;
	--cnvs-body-font: 'Roboto', sans-serif;
	--cnvs-secondary-font: '<PERSON>tera<PERSON>', serif;
}

* {
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
}

body.dark,
.dark #wrapper,
.dark #content,
.dark #header:not(.transparent-header),
#header.dark:not(.transparent-header),
.dark #header-wrap:not(.not-dark) {
	background-color: #111;
}

.entry::after { background-color: #EEE !important; }
.dark .entry::after { background-color: rgba(255,255,255,0.15) !important; }

.dark .header-misc-icon > a { color: #EEE; }

.header-size-custom #logo img {
	height: 70px;
}

.date-today {
	position: relative;
	margin-top: -1.25rem;
	margin-bottom: 1.5rem;
}

.date-today .divider-text {
	color: #777;
	font-size: 0.875rem !important;
	line-height: 1.3;
}

.date-today::after,
.date-today::before {
	background-color: transparent;
	height: 0;
	border-top: 4px double #DDD;
}

.ad-banner {
	text-align: center;
	max-width: 970px;
	margin: 0 auto;
}

.icon-dark { font-family: 'bootstrap-icons'; }
.dark .dark-mode .icon-dark::before { content: "\F5A1"; }
.dark-mode .icon-dark::before { content: "\F495"; }

.dropdown-langs .dropdown-item {
	font-size: 14px;
}

.dropdown-langs img {
	margin-right: 8px;
	width: 18px;
	height: 18px;
}

.dropdown-langs .dropdown-toggle::after {
	position: relative;
	font-family: 'bootstrap-icons';
	content: "\F229";
	font-size: 10px;
	top: 3px;
	border: 0;
	color: #666;
	margin-left: -5px;
}

.social-icon.si-mini {
	--cnvs-socialicon-size: 1.75rem;
	--cnvs-socialicon-fontsize: 0.75rem;
	margin-bottom: 0;
}

.entry-title h3 {
	text-transform: none;
	letter-spacing: 0;
	font-weight: 500;
	font-size: 25px;
}

.entry-title.title-sm h2,
.entry-title.title-sm h3,
.entry-title.title-xs h2,
.entry-title.title-xs h3 {
	font-size: 1.25rem;
	line-height: 1.6;
}
.entry-title.title-xs h2,
.entry-title.title-xs h3,
.entry-title h4 {
	font-size: 1.15rem;
}

.entry-title h4 {
	font-size: 1rem;
	font-weight: 400;
}

.entry-title h2 a,
.entry-title h2 a:hover,
.entry-title h3 a,
.entry-title h3 a:hover,
.entry-title h4 a:hover  { color: var(--cnvs-contrast-900) !important; }

:not(.dark) .entry-categories a {
	display: block;
	text-transform: uppercase;
	font-size: 11px;
	color: #333;
	font-weight: 700;
	letter-spacing: 1px;
	margin-bottom: 10px;
}

.dark .entry-categories a {
	color: var(--cnvs-themecolor);
}

.entry:not(:last-child)::after {
	--cnvs-post-item-margin: 30px;
	display: block;
}

.entry-meta {
	display: block;
	float: none;
	margin: 8px 0 0 0 !important;
}

.entry-content {
	margin-top: 25px;
}

.entry-content p {
	color: #555;
}

.dark .entry-content p { color: rgba(255, 255, 255, 0.7); }

.posts-md .entry-meta,
.posts-md .entry-meta a,
.posts-sm .entry-meta,
.posts-sm .entry-meta a {
	font-size: 14px;
}

.dark .entry-meta li a { color: #AAA !important; }

/* .posts-sm .entry:not(:first-child) .grid-inner,
.posts-md .entry:not(:first-child) .grid-inner {
	padding-top: 1.5rem;
	margin-top: 2rem;
	border-top: 1px solid #E5E5E5;
}

.dark .posts-sm .entry:not(:first-child) .grid-inner,
.dark .posts-md .entry:not(:first-child) .grid-inner {
	border-top-color: rgba(255, 255, 255, 0.25);
} */

.week-posts {
	list-style: none;
}

.week-posts li .entry-image {
	display: none;
}

.week-posts > li {
	counter-increment: step-counter;
}

.week-posts > li .grid-inner::before {
	content: "0" counter(step-counter)'.';
	position: absolute;
	left: 0;
	font-size: 26px;
	font-weight: 700;
	font-family: var(--cnvs-secondary-font);
	color: #DDD;
	line-height: 1;
}

.week-posts > li .grid-inner {
	padding-left: 48px;
}

.dark .week-posts > li::before { color: rgba(255,255,255,0.3); }

.play-icon {
	position: absolute;
	bottom: 15px;
	left: 15px;
	color:  #EEE;
	text-transform: uppercase;
	font-size: 12px;
	letter-spacing: 1px;
	font-weight: 500;
}

.play-icon i {
	position: relative;
	width: 30px;
	height: 30px;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.85);
	text-align: center;
	line-height: 28px;
	color: #222;
	padding: 1px 0 0 3px;
	font-size: 10px;
	margin-right: 5px;
}

.color-underline {
	background-image: linear-gradient(rgba(var(--cnvs-themecolor-rgb, 243, 152, 135), 0.3), rgba(var(--cnvs-themecolor-rgb, 243, 152, 135), 0.3));
	background-repeat: no-repeat;
	background-size: 0 8px;
	background-position: 0 82%;
	padding: 0 2px 2px 0;
	transition: background .6s cubic-bezier(.19,1,.22,1);
}

.color-underline:hover { background-size: 100% 8px; }

.dark .color-underline,
.dark .widget > h4,
.dark .menu-item .menu-link {
	background-image: linear-gradient(rgba(255,255,255, 0.25), rgba(255,255,255, 0.25));
}

/* .fancy-title.title-border::before {
	border-top: 2px solid #888;
	top: 50%;
	margin-top: -1px;
} */

a.more-link {
	font-style: normal;
	border-bottom: none;
	font-size: 0.875rem;
	color: var(--cnvs-contrast-900);
	font-weight: 500;
	text-transform: capitalize;
	text-decoration: underline !important;
}

a.more-link i {
	font-size: 0.875rem;
	margin-left: 0.375rem;
}

.section-colored { background-color: #FFEAE6; }

.page-title {
	background-color: #ecf2f4;
}

.dark.section {
	background-color: #010101;
}

.dark .section {
	background-color: rgba(255,255,255,0.05);
}

.widget_links li a {
	background-image: none !important;
	padding-left: 7px;
}

.widget > h4 {
	display: inline-block;
	background-image: linear-gradient(rgba(var(--cnvs-themecolor-rgb, 243, 152, 135), 0.3), rgba(var(--cnvs-themecolor-rgb, 243, 152, 135), 0.3));
	background-repeat: no-repeat;
	background-size: 100% 6px;
	background-position: 0% 95%;
}

#footer {
	background-color: rgba(255, 234, 230, 0.32);
	border-top: 0;
}

.dark #footer {
	background-color: #000;
}

#copyrights {
	background-color: #FFF;
}

.dark #copyrights { background-color: rgba(255, 255, 255, 0.08); }


/* Responsive Device more than 992px (.device-md >)
-----------------------------------------------------------------*/
@media (min-width: 992px) {

	#header,
	#header-wrap {
		border: 0;
	}

	#header.sticky-header #header-wrap {
		box-shadow: none;
	}

	.header-size-custom #logo img {
		height: 100px;
	}

	.header-size-custom .header-wrap-clone {
		height: calc( 122px + 70px + 2px );
	}

	.header-size-custom .menu-container > .menu-item > .menu-link {
		padding-top: 24px; /* (70px – 22px)/2 */
		padding-bottom: 24px; /* (70px – 22px)/2 */
	}

	.menu-link {
		text-transform: none;
		letter-spacing: 0;
		font-size: 1rem;
		font-weight: 500;
	}

	.primary-menu {
		width: 80%;
		border-top: 0;
	}

	.header-row.header-border {
		border-top: 1px solid #E5E5E5;
		border-bottom: 1px solid #E5E5E5;
	}

	.dark .header-row.header-border {
		border-top-color: rgba(255,255,255,0.15);
		border-bottom-color: rgba(255,255,255,0.15);
	}

	.sticky-header #header-wrap {
		border-bottom: 1px solid #E5E5E5;
	}

	.dark.sticky-header #header-wrap {
		border-bottom-color: rgba(255,255,255,0.15);
	}

	.sticky-header .header-row.header-border {
		border-bottom: 0;
	}

	.menu-item .menu-link {
		background-image: linear-gradient(rgba(var(--cnvs-themecolor-rgb, 243, 152, 135), 0.3), rgba(var(--cnvs-themecolor-rgb, 243, 152, 135), 0.3));
		background-repeat: no-repeat;
		background-size: 0% 7px;
		background-position: 0 58%;
		transition: background-size .6s cubic-bezier(.19,1,.22,1);
		padding-left: 1px;
		padding-right: 1px;
		color: #222 !important;
	}

	.dark .menu-item .menu-link { color: #EEE !important; }

	.menu-item.current .menu-link,
	.menu-item:hover .menu-link {
		background-size: 100% 7px;
	}

	.top-search-open #top-search form { height: 100px !important; }
	.top-search-open .social-icons { opacity: 0; z-index: -2; }
	body.top-search-open .primary-menu { opacity: 1 !important; }

	.border-between > [class*='col-']::before {
	   background: #E5E5E5;
	   bottom: 0;
	   content: " ";
	   left: 0;
	   position: absolute;
	   width: 1px;
	   top: 0;
	}

	.dark .border-between > [class*='col-']::before { background: rgba(255, 255, 255, 0.2); }

	.border-between > [class*='col-']:first-child::before { display: none; }

	/* Re-Edit Bootstrap */
	.border-between > [class*='col-'],
	.border-between .container,
	.border-between .container-fluid {
		padding-right: 30px;
		padding-left: 30px;
	}

	.border-between.row {
		margin-right: -30px;
		margin-left: -30px;
	}
}

.btn-sm,
.btn-group-sm > .btn { line-height: 2; }


/* SINGLE PAGE */

.single-post { margin-top: 20px; }

.single-post .entry .entry-title,
.single-post .entry .entry-title h2 {
	max-width: 750px;
	margin: 0 auto;
	text-align: center;
	text-transform: none;
	letter-spacing: -1px;
	font-size: 34px;
}

.single-post .entry .entry-image {
	margin: 25px 0 35px;
}

.single-post .entry-content .text-content {
	position: relative;
	display: block;
	margin-top: 20px;
}

.single-post .entry-content .text-content p {
	font-size: 18px;
	line-height: 1.65 !important;
}

.single-post .entry-content ol.list-numbers {
	padding-left: 15px;
	margin: 30px 20px;
}

.single-post .entry-content ol.list-numbers li {
	font-size: 19px;
	line-height: 1.5;
	list-style-type: decimal;
	margin-top: 1.5rem;
	font-family: var(--cnvs-primary-font,  'Domine', serif);
}

.single-post .media-content .entry-title { display: none; }

.single-post .si-share {
	display: block;
	border-bottom: 1px solid #EEE;
	margin-bottom: 20px;
	padding: 0 0 20px 0;
}

.single-post .si-share h5 {
	font-weight: 500;
	opacity: .7;
	margin-bottom: 8px;
}

.related-posts .entry-title h3 {
	font-size: 20px;
	margin-top: 10px;
}

#comments label {
	text-transform: none;
	letter-spacing: 0;
	font-weight: 400;
	margin-bottom: 5px;
	opacity: .7;
}

.comment-content p {
	font-size: 17px;
	line-height: 1.6 !important;
}

.comment-content .comment-author span,
.comment-content .comment-author span a {
	font-style: normal;
	font-family: var(--cnvs-primary-font);
	margin-top: 4px;
}

/* Responsive Device more than 992px (.device-md >)
-----------------------------------------------------------------*/
@media (min-width: 992px) {
	.single-post .media-content .entry-title {
		display: block;
		border-bottom: 1px solid #EEE;
		margin-top: 20px;
		margin-bottom: 20px;
		padding-bottom: 20px;
	}

	.single-post .media-content .entry-title h4 {
		font-size: 15px;
		line-height: 1.6;
		margin-bottom: 0;
	}

	.single-post .si-share {
		display: block;
		border-bottom: none;
		margin-bottom: 0;
		padding-bottom: 0;
	}
}

@media (min-width: 1200px) {
	.single-post .entry-content .media-content {
		display: block;
		position: -webkit-sticky;
		position: sticky;
		top: 214px;
		left: 0;
		height: 100%;
		max-width: 200px;
	}

	/*.single-post .entry-content .text-content {
		max-width: 700px;
		margin-top: -170px;
		margin-right: auto;
		margin-left: auto;
	}*/
}


.page-title h1 {
	letter-spacing: 3px;
	font-family: var(--cnvs-body-font);
}

.page-title.page-title-center span {
	margin-top: 20px;
	max-width: 600px;
}

.widget-search input,
.widget-search .btn { border-color: #DDD; }

.dark .widget-search .form-control:not(.not-dark),
.dark .widget-search .btn {
	border-color: rgba(255, 255, 255, 0.1);
}

.widget-nav .nav { flex-direction: row; }

.widget-nav .nav .nav-item .nav-link {
	padding: 5px 10px;
	color: #444;
	line-height: 1.3;
	font-weight: 500;
	transition: all .2s ease;
}

.dark .widget-nav .nav .nav-item .nav-link { color: #DDD; }

.widget-nav .nav .nav-item.active .nav-link,
.widget-nav .nav .nav-item:hover .nav-link {
	border-left-color: var(--cnvs-themecolor);
	color: var(--cnvs-themecolor);
}

.dark .form-control:not(.not-dark), .dark .sm-form-control:not(.not-dark) {
	background-color: rgba(255,255,255,0.02);
	border-color: rgba(255,255,255,0.16);
	color: #EEE;
}

.dark .form-control:not(.not-dark):active,
.dark .form-control:not(.not-dark):focus,
.dark .sm-form-control:not(.not-dark):active,
.dark .sm-form-control:not(.not-dark):focus {
	border: 2px solid rgba(255,255,255,0.2) !important;
}

/* Responsive Device more than 992px (.device-md >)
-----------------------------------------------------------------*/
@media (min-width: 992px) {

	.content-wrap { overflow: inherit; }

	.widget-nav .nav { flex-direction: column; }

	.widget-nav .nav .nav-item .nav-link {
		padding: 5px 0 5px 15px;
		border-left: 1px solid #DDD;
		line-height: 1.8;
	}

	.dark .widget-nav .nav .nav-item .nav-link,
	.dark #header.sticky-header #header-wrap,
	.dark .single-post .media-content .entry-title { border-color: rgba(255, 255, 255, 0.1); }
}

@media (max-width: 991.98px) {
	.cat-widgets.position-sticky {
		position: relative !important;
		top: 0;
		background-color: #F9F9F9;
		z-index: 9;
		padding: 3rem;
		top: 0 !important;
		width: 100%;
	}
}